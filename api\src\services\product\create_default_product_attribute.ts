import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const createDefaultProductAttribute = async ({
  defaultProductAttribute,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.post(
      `${process.env.LCA_API_ENDPOINT}/product-attributes/${tenantID}`,
      sanitizeInput(defaultProductAttribute)
    )

    return {
      id: response.data.id,
      key: response.data.key,
    }
  } catch (error) {
    let errorMessage =
      'Error creating default product attribute. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
