import React, { useRef } from 'react'
import { useReactToPrint } from 'react-to-print'
import type { SuppliersQuery } from 'types/graphql'
import type { CellSuccessProps, CellFailureProps } from '@redwoodjs/web'
import { Image, Row, Col, Tag, Flex, Card, Button } from 'antd'
import moment from 'moment'
import { saveAs } from 'file-saver'
import DataTable from '../DataTable/DataTable'
import {
  ArrowLeftOutlined,
  PrinterOutlined,
  DownloadOutlined,
} from '@ant-design/icons'
import { navigate, routes } from '@redwoodjs/router'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import { Bar } from '@ant-design/plots'
import './print.css'
import ErrorHandler from '../ErrorHandler/ErrorHandler'
import { renderImpactFactorUnit } from 'src/utils/helper'

export const QUERY = gql`
  query SuppliersQuery {
    getSuppliers {
      supplierName
      supplierId
      supplierLevel
      supplierType
      country
      city
      address
      totalProducts
      products {
        productName
        productId
        category
        brand
        totalEmissions
        scope3Emissions
        rawMaterialEmissions {
          ingredientName
          totalEmissions
          ingredientEmissions
        }
        packagingEmissions {
          packageMaterial
          totalEmissions
        }
        ingredientDistributionEmissions {
          ingredientName
          totalEmissions
          transportEmissions
        }
        packagingDistributionEmissions {
          packageMaterial
          totalEmissions
          transportEmissions
        }
        annualSalesVolumeUnits
      }
      scope3Emissions
    }
  }
`

export const Loading = () => <div>Loading...</div>

export const Failure = ({ error }: CellFailureProps) => (
  <ErrorHandler error={error} />
)

export const Success = ({ getSuppliers }: CellSuccessProps<SuppliersQuery>) => {
  if (!getSuppliers) {
    return <ErrorHandler />
  }

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const orgName =
    orgMemberInfo && orgMemberInfo.orgName ? orgMemberInfo.orgName : null

  const componentRef = useRef()
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
  })

  let barStackedPlotData = [],
    supplierEmissionsTableData = []

  getSuppliers.forEach((supplier) => {
    const { supplierName, supplierId, country } = supplier

    const supplierEmissionsData = supplier.products.map((product) => ({
      supplier: supplierName,
      emissions: parseFloat(product.scope3Emissions.toFixed(4)),
      product: product.productName,
    }))

    barStackedPlotData = barStackedPlotData.concat(supplierEmissionsData)

    const supplierData = {
      supplierId,
      supplierName,
      country,
      supplierLevel: supplier.supplierLevel,
      supplierType: supplier.supplierType,
      scope3Emissions: supplier.scope3Emissions.toFixed(4),
    }

    supplierEmissionsTableData.push(supplierData)
  })

  supplierEmissionsTableData.sort((a, b) =>
    a.supplierName.localeCompare(b.supplierName)
  )

  const barStackedPlotConfig = {
    data: barStackedPlotData.sort((a, b) => b.emissions - a.emissions),
    isStack: true,
    xField: 'emissions',
    yField: 'supplier',
    height: 800,
    seriesField: 'product',
    legend: false,
    xAxis: {
      label: {
        formatter: (text) => `${text} tCO2`,
      },
    },
  }

  const columns = [
    {
      title: 'Supplier ID',
      dataIndex: 'supplierId',
    },
    {
      title: 'Supplier Name',
      dataIndex: 'supplierName',
      width: '30%',
    },
    {
      title: 'Country',
      dataIndex: 'country',
    },
    {
      title: 'Supplier Tier',
      dataIndex: 'supplierLevel',
    },
    {
      title: 'Supplier Type',
      dataIndex: 'supplierType',
    },
    {
      title: `Scope 3 Emissions (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'scope3Emissions',
      render: (text: string) => (
        <p>
          {parseFloat(text).toLocaleString(undefined, {
            minimumFractionDigits: 4,
          })}
        </p>
      ),
    },
  ]

  const exportToCsv = (suppliers) => {
    const headers = columns.map((x) => x.title)
    const csvData = [headers]

    suppliers.forEach((supplier) => {
      const row = [
        supplier.supplierId,
        supplier.supplierName,
        supplier.country,
        supplier.supplierLevel,
        supplier.supplierType,
        supplier.scope3Emissions,
      ]
      csvData.push(row)
    })

    const blob = new Blob([csvData.join('\n')], {
      type: 'text/csv;charset=utf-8',
    })
    saveAs(blob, 'CarbonBright_Product_Emissions_By_Supplier_Report.csv')
  }

  return (
    <>
      <Row style={{ marginTop: '10px' }}>
        <Col flex="auto">
          <Button
            type="link"
            style={{ color: 'black' }}
            onClick={() => navigate(routes.reports())}
          >
            <b>
              <ArrowLeftOutlined /> &nbsp;Back
            </b>
          </Button>
        </Col>
        <Col flex="0">
          <Flex gap="small">
            <Button
              type="link"
              style={{ color: 'black' }}
              onClick={handlePrint}
            >
              <p style={{ fontSize: '18px' }}>
                <PrinterOutlined />
              </p>
            </Button>
            <Button
              type="link"
              style={{ color: 'black' }}
              onClick={() => exportToCsv(supplierEmissionsTableData)}
            >
              <p style={{ fontSize: '18px' }}>
                <DownloadOutlined />
              </p>
            </Button>
          </Flex>
        </Col>
      </Row>
      <Card ref={componentRef} style={{ marginTop: '10px' }}>
        <Row>
          <Col flex="auto">
            <h1 style={{ fontSize: '18px', fontWeight: 'bold' }}>
              Product Emissions by Supplier
            </h1>
          </Col>
          <Col flex="0">
            <Image
              width={150}
              preview={false}
              alt="CarbonBright"
              src="/images/logo.svg"
            />
          </Col>
        </Row>
        <Row>
          <Col flex="auto">
            <p style={{}}>Company: {orgName}</p>
          </Col>
          <Col flex="12">
            <p style={{}}>Creation Date: {moment().format('DD/MM/YYYY')}</p>
          </Col>
        </Row>
        <Card style={{ marginTop: '10px' }} title="Product Category Emissions">
          <Bar {...barStackedPlotConfig} />
        </Card>
        <DataTable
          style={{ marginTop: '20px' }}
          key={supplierEmissionsTableData.length}
          data={supplierEmissionsTableData}
          columns={columns}
          paginate={false}
          scroll={null}
        />
      </Card>
    </>
  )
}
