import { parseJWT, Decoded } from '@redwoodjs/api'
import { AuthenticationError, ForbiddenError } from '@redwoodjs/graphql-server'
import { initBaseAuth } from '@propelauth/node'
import { OrgMemberInfo } from '@propelauth/javascript'
import Sentry from 'src/lib/sentry'

/**
 * Represents the user attributes returned by the decoding the
 * Authentication provider's JWT together with an optional list of roles.
 */
type RedwoodUser = Record<string, unknown> & {
  roles?: string[]
  orgMemberInfo?: OrgMemberInfo
}

// Cache implementation for user and org metadata
interface CacheItem<T> {
  value: T
  expiresAt: number
}
interface Cache<T> {
  [key: string]: CacheItem<T>
}

// Cache TTL in milliseconds (default: 5 minutes)
const CACHE_TTL = process.env.CACHE_TTL ? parseInt(process.env.CACHE_TTL) : 5 * 60 * 1000

// Init caches
const userCache: Cache<any> = {}
const orgCache: Cache<any> = {}

// Cache helper metheds
const getCachedItem = <T>(cache: Cache<T>, key: string): T | null => {
  const item = cache[key]
  if (!item) return null

  const now = Date.now()
  if (now > item.expiresAt) {
    // Cache expired
    delete cache[key]
    return null
  }

  return item.value
}

const setCachedItem = <T>(cache: Cache<T>, key: string, value: T, ttl: number = CACHE_TTL): void => {
  cache[key] = {
    value,
    expiresAt: Date.now() + ttl
  }
}

const clearCache = (): void => {
  Object.keys(userCache).forEach(key => delete userCache[key])
  Object.keys(orgCache).forEach(key => delete orgCache[key])
}

const {
  validateAccessTokenAndGetUser,
  fetchUserMetadataByEmail,
  fetchUserMetadataByUserId,
  fetchOrg,
  updateOrg,
  updateUserMetadata,
} = initBaseAuth({
  authUrl: process.env.PROPELAUTH_AUTH_URL,
  apiKey: process.env.PROPELAUTH_API_TOKEN,
  // manualTokenVerificationMetadata: {
  //   verifierKey: process.env.PROPELAUTH_VERIFIER_KEY,
  //   issuer: process.env.PROPELAUTH_AUTH_URL,
  // },
})

const retry = async (fn, maxRetries = 3, delay = 300) => {
  let lastError;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      // Only retry on network errors or 5xx errors, not on 4xx
      if (error.status && error.status < 500 && error.status !== 429) {
        throw error;
      }

      // Exponential backoff with jitter
      const jitter = Math.random() * 0.3 + 0.8; // 0.8-1.1
      const waitTime = delay * Math.pow(2, attempt) * jitter;

      console.log(`Retry attempt ${attempt + 1}/${maxRetries} after ${waitTime}ms`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  throw lastError;
};

// Cached versions of PropelAuth API functions
const fetchUserMetadataByEmailCached = async (email: string) => {
  const cacheKey = `email:${email}`
  const cachedUser = getCachedItem(userCache, cacheKey)

  if (cachedUser) {
    return cachedUser
  }

  const userInfo = await retry(() => fetchUserMetadataByEmail(email))
  if (userInfo) {
    setCachedItem(userCache, cacheKey, userInfo)
    // Also cache by userId for other lookups ex: fetch user metadata by userId
    if (userInfo.userId) {
      setCachedItem(userCache, `userId:${userInfo.userId}`, userInfo)
    }
  }

  return userInfo
}

const fetchUserMetadataByUserIdCached = async (userId: string) => {
  const cacheKey = `userId:${userId}`
  const cachedUser = getCachedItem(userCache, cacheKey)

  if (cachedUser) {
    return cachedUser
  }

  const userInfo = await retry(() => fetchUserMetadataByUserId(userId))
  if (userInfo) {
    setCachedItem(userCache, cacheKey, userInfo)
    // Also cache by email for other lookups ex: fetch user metadata by email
    if (userInfo.email) {
      setCachedItem(userCache, `email:${userInfo.email}`, userInfo)
    }
  }

  return userInfo
}

const fetchOrgCached = async (orgId: string) => {
  const cacheKey = `org:${orgId}`
  const cachedOrg = getCachedItem(orgCache, cacheKey)

  if (cachedOrg) {
    return cachedOrg
  }

  const orgInfo = await retry(() => fetchOrg(orgId))
  if (orgInfo) {
    setCachedItem(orgCache, cacheKey, orgInfo)
  }

  return orgInfo
}

export const authDecoder = async (token: string, type: string) => {
  if (type != 'custom-auth') {
    return null
  }

  const decoded = await retry(() => validateAccessTokenAndGetUser(`Bearer ${token}`))
  const userInfo = await fetchUserMetadataByEmailCached(decoded.email)
  return {
    ...decoded,
    ...userInfo,
  } as Decoded
}

/**
 * getCurrentUser returns the user information together with
 * an optional collection of roles used by requireAuth() to check
 * if the user is authenticated or has role-based access
 *
 * !! BEWARE !! Anything returned from this function will be available to the
 * client--it becomes the content of `currentUser` on the web side (as well as
 * `context.currentUser` on the api side). You should carefully add additional
 * fields to the return object only once you've decided they are safe to be seen
 * if someone were to open the Web Inspector in their browser.
 *
 * @see https://github.com/redwoodjs/redwood/tree/main/packages/auth for examples
 *
 * @param decoded - The decoded access token containing user info and JWT
 *   claims like `sub`. Note, this could be null.
 * @param { token, SupportedAuthTypes type } - The access token itself as well
 *   as the auth provider type
 * @param { APIGatewayEvent event, Context context } - An optional object which
 *   contains information from the invoker such as headers and cookies, and the
 *   context information about the invocation such as IP Address
 * @returns RedwoodUser
 */

function addDaysToTimestamp(timestamp, days): number {
  const date = new Date(timestamp)
  date.setDate(date.getDate() + days)
  return date.getTime()
}

function getDaysDifference(timestamp1: number, timestamp2: number): number {
  const date1 = new Date(timestamp1)
  const date2 = new Date(timestamp2)

  if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
    throw new Error('Invalid date format')
  }

  const differenceInMilliseconds = date2.getTime() - date1.getTime()
  const millisecondsPerDay = 1000 * 60 * 60 * 24
  const differenceInDays = Math.round(
    differenceInMilliseconds / millisecondsPerDay
  )
  return differenceInDays
}

const getUserTrialInfo = function (userMetaData) {
  const trialInfo = {
    isTrialUser: false,
    trialPeriodDays: null,
    trialEndDate: null,
    daysRemaining: null,
    hasTrialExpired: false,
  }

  if (!userMetaData) {
    return trialInfo
  }

  if (userMetaData.metadata?.isTrialUser) {
    trialInfo.isTrialUser = true
    trialInfo.trialPeriodDays = userMetaData.metadata?.trialPeriodDays ?? 0
    trialInfo.trialEndDate = addDaysToTimestamp(
      userMetaData.createdAt * 1000,
      trialInfo.trialPeriodDays
    )
    trialInfo.daysRemaining = getDaysDifference(
      new Date().getTime(),
      trialInfo.trialEndDate
    )
    if (trialInfo.daysRemaining < 0) {
      trialInfo.hasTrialExpired = true
    }
  }

  return trialInfo
}

export const UpdateOrgMetadata = async (orgId, metadataKey, metadataValue) => {
  try {
    const orgInfo = await fetchOrgCached(orgId)

    if (!orgInfo) {
      throw new Error('Org not found')
    }

    const response = await updateOrg({
      orgId,
      metadata: {
        ...orgInfo.metadata,
        [metadataKey]: metadataValue,
      },
    })

    // Invalidate org cache after update
    delete orgCache[`org:${orgId}`]

    return response
  } catch (error) {
    console.error(error)
    throw error
  }
}

export const UpdateUserMetadata = async (
  userId,
  metadataKey,
  metadataValue
) => {
  try {
    const userInfo = await fetchUserMetadataByUserIdCached(userId)

    if (!userInfo) {
      throw new Error('User not found')
    }

    const response = await updateUserMetadata(userId, {
      metadata: {
        ...userInfo.metadata,
        [metadataKey]: metadataValue,
      },
    })

    // Invalidate user cache after update
    delete userCache[`userId:${userId}`]
    if (userInfo.email) {
      delete userCache[`email:${userInfo.email}`]
    }

    return response
  } catch (error) {
    console.error(error)
    throw error
  }
}

export const getCurrentUser = async (
  decoded: Decoded
): Promise<RedwoodUser | null> => {
  try {
    if (!decoded) {
      return null
    }

    const { roles } = parseJWT({ decoded })

    const orgs = decoded.orgIdToOrgMemberInfo
    if (!orgs) {
      console.error('No organization information found for user')
    }

    const orgId = Object.keys(orgs)[0] // For now, we only support one org per user
    if (!orgId) {
      console.error('No organization ID found for user')
    }

    const orgMemberInfo = orgs[orgId] as OrgMemberInfo
    if (!orgMemberInfo) {
      console.error('No organization member information found')
    }

    Sentry.setUser(decoded)

    const userTrialInfo = getUserTrialInfo(decoded)

    if (roles) {
      return {
        ...decoded,
        orgMemberInfo,
        roles,
        userTrialInfo,
      }
    }

    return {
      ...decoded,
      orgMemberInfo,
      userTrialInfo,
    }
  } catch (error) {
    console.log(error)
    Sentry.captureException(error)
    throw error
  }
}

/**
 * The user is authenticated if there is a currentUser in the context
 *
 * @returns {boolean} - If the currentUser is authenticated
 */
const labsOnlyOperations = [
  'predictEmissionsFactorsQuery',
  'ContactSupport',
  'SubmitFeedback',
]
export const isAuthenticated = (): boolean => {
  // If the user is not associated with an organization, they should not be able to access any other API other than Labs API
  if (
    !context.currentUser.orgMemberInfo ||
    !Object.keys(context.currentUser.orgMemberInfo).length
  ) {
    if (!labsOnlyOperations.includes(context.params?.operationName)) {
      return false
    }
  }

  if (!['UpgradeTrial'].includes(context.params?.operationName)) {
    if (context.currentUser.userTrialInfo.hasTrialExpired) {
      return false
    }
  }
  return !!context.currentUser
}

/**
 * When checking role membership, roles can be a single value, a list, or none.
 * You can use Prisma enums too (if you're using them for roles), just import your enum type from `@prisma/client`
 */
type AllowedRoles = string | string[] | undefined

/**
 * Checks if the currentUser is authenticated (and assigned one of the given roles)
 *
 * @param roles: {@link AllowedRoles} - Checks if the currentUser is assigned one of these roles
 *
 * @returns {boolean} - Returns true if the currentUser is logged in and assigned one of the given roles,
 * or when no roles are provided to check against. Otherwise returns false.
 */
export const hasRole = (roles: AllowedRoles): boolean => {
  if (!isAuthenticated()) {
    return false
  }

  const currentUserRoles = context.currentUser?.roles

  if (typeof roles === 'string') {
    if (typeof currentUserRoles === 'string') {
      // roles to check is a string, currentUser.roles is a string
      return currentUserRoles === roles
    } else if (Array.isArray(currentUserRoles)) {
      // roles to check is a string, currentUser.roles is an array
      return currentUserRoles?.some((allowedRole) => roles === allowedRole)
    }
  }

  if (Array.isArray(roles)) {
    if (Array.isArray(currentUserRoles)) {
      // roles to check is an array, currentUser.roles is an array
      return currentUserRoles?.some((allowedRole) =>
        roles.includes(allowedRole)
      )
    } else if (typeof currentUserRoles === 'string') {
      // roles to check is an array, currentUser.roles is a string
      return roles.some((allowedRole) => currentUserRoles === allowedRole)
    }
  }

  // roles not found
  return false
}

/**
 * Use requireAuth in your services to check that a user is logged in,
 * whether or not they are assigned a role, and optionally raise an
 * error if they're not.
 *
 * @param roles?: {@link AllowedRoles} - When checking role membership, these roles grant access.
 *
 * @returns - If the currentUser is authenticated (and assigned one of the given roles)
 *
 * @throws {@link AuthenticationError} - If the currentUser is not authenticated
 * @throws {@link ForbiddenError} - If the currentUser is not allowed due to role permissions
 *
 * @see https://github.com/redwoodjs/redwood/tree/main/packages/auth for examples
 */
export const requireAuth = ({ roles }: { roles?: AllowedRoles } = {}) => {
  if (!isAuthenticated()) {
    throw new AuthenticationError("You don't have permission to do that.")
  }

  if (roles && !hasRole(roles)) {
    throw new ForbiddenError("You don't have access to do that.")
  }
}
