export const getAncestorNode = (nodes, edges, node, nodeTypeField = 'nodeType') => {
  if (nodeTypeField == 'nodeType') {
    const ancestorNodeId = edges.find((x) => x.toNodeId === node.id)?.fromNodeId
    if (!ancestorNodeId) return null

    return nodes.find((x) => x.id === ancestorNodeId)
  }

  const ancestorNodeId = edges.find((x) => x.to_node_id === node.id)?.from_node_id
  if (!ancestorNodeId) return null

  return nodes.find((x) => x.id === ancestorNodeId)
}

export const getAncestorNodeOfType = (
  nodes: any[],
  edges: any[],
  node: any,
  nodeTypes: string[],
  nodeTypeField = 'nodeType',
) => {
  const ancestorNode = getAncestorNode(nodes, edges, node, nodeTypeField)
  if (!ancestorNode) return null

  return nodeTypes.includes(ancestorNode[nodeTypeField])
    ? ancestorNode
    : getAncestorNodeOfType(nodes, edges, ancestorNode, nodeTypes, nodeTypeField)
}

export const getDescendantNode = (nodes, edges, node, filterByNodeName = false) => {
  if (filterByNodeName) {
    const descendantNodeName = edges.find((x) => x.from_node_name === node.name)?.to_node_name
    if (!descendantNodeName) return null

    return nodes.find((x) => x.name === descendantNodeName)
  }

  const descendantNodeId = edges.find((x) => x.fromNodeId === node.id)?.toNodeId
  if (!descendantNodeId) return null

  return nodes.find((x) => x.id === descendantNodeId)

}

interface DescendantNodeOptions {
  nodes: any[]
  edges: any[]
  node: any
  nodeTypes: string[]
  nodeTypeField?: string
  filterByNodeName?: boolean
}

export const getDescendantNodeOfType = ({
  nodes,
  edges,
  node,
  nodeTypes,
  nodeTypeField = 'nodeType',
  filterByNodeName = false,
}: DescendantNodeOptions) => {
  const descendantNode = getDescendantNode(nodes, edges, node, filterByNodeName)
  if (!descendantNode) return null

  return nodeTypes.includes(descendantNode[nodeTypeField])
    ? descendantNode
    : getDescendantNodeOfType({
        nodes,
        edges,
        node: descendantNode,
        nodeTypes,
        nodeTypeField,
        filterByNodeName,
      })
}

export const findLastNode = (nodes, edges, nodeType = null, nodeTypeField = 'nodeType') => {
  const sourceNodes = new Set(edges.map((edge) => edge.fromNodeId))
  const destNodes = new Set(edges.map((edge) => edge.toNodeId))

  let lastNodes = nodes.filter((node) => (destNodes.has(node.id) && !sourceNodes.has(node.id)))

  if (nodeType) {
    lastNodes = lastNodes.filter((node) => node[nodeTypeField] === nodeType)
  }

  if (!lastNodes?.length) {
    throw new Error(`No end node found ${nodeType ? `with type ${nodeType}` : ''} in the graph`)
  }

  if (lastNodes?.length > 1) {
    throw new Error(`Multiple end nodes found with type ${nodeType}`)
  }

  return lastNodes[0]
}

export const transformNode = (node) => ({
  id: node.id,
  name: node.name,
  component_name: node.component,
  description: node.description,
  packaging_level: node.packagingLevel,
  recycled_content_rate: node.recycledContentRate,
  node_type: node.nodeType,
  scrap_rate: node.scrapRate,
  scrap_fate: node.scrapFate,
  location: node.location
    ? {
        city: node.location?.city,
        country: node.location?.country,
      }
    : null,
  emissions_factor: node.emissionsFactor
    ? {
        activity_name: node.emissionsFactor.activityName,
        reference_product_name: node.emissionsFactor.referenceProduct,
        geography: node.emissionsFactor.geography,
        source: node.emissionsFactor.source,
      }
    : null,
  amount: node.amount ?? 0,
  unit: node.unit,
  quantity: node.quantity,
  supplier_id: node.supplierId,
})
