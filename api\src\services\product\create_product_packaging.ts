import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const createProductPackaging = async ({
  productId,
  productPackaging,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.post(
      `${process.env.LCA_API_ENDPOINT}/products/${tenantID}/${sanitizeInput(productId)}/product-packaging`,
      sanitizeInput(productPackaging)
    )

    return {
      packagingMaterial: response.data.packaging_material,
      packagingItem: response.data.packaging_item,
      weight: response.data.weight_grams,
      packagingLevel: response.data.packaging_level,
    }
  } catch (error) {
    let errorMessage = 'Error creating product packaging. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
