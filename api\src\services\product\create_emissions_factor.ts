import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const createEmissionsFactor = async ({ emissionsFactor }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.post(
      `${process.env.LCA_API_ENDPOINT}/emissions-factors/${tenantID}`,
      sanitizeInput(emissionsFactor)
    )

    return {
      activityName: response.data.activity_name,
      activityType: response.data.activity_type,
      description: response.data.activity_description,
      referenceProduct: response.data.reference_product,
      geography: response.data.geography,
      source: response.data.source,
      kgCO2e: response.data.kg_co2e,
      unit: response.data.unit,
    }
  } catch (error) {
    console.log(error)
    let errorMessage = 'Error creating emissions factor. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
