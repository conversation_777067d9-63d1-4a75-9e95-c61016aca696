import axios from 'axios'
import { base64ToBlob } from 'src/lib/helper'
import { GraphQLError } from 'graphql'
import Sentry from 'src/lib/sentry'

export const extractComponentsFromFile = async ({
  base64Data,
  contentType,
}) => {
  try {
    const blob = base64ToBlob(base64Data, contentType)

    const formData = new FormData()
    formData.append('file', blob, 'product_info')

    let headers = {
      'Content-Type': 'multipart/form-data',
      'X-Use-Graph-Extraction': 'true',
      'X-Parts-Only': 'true',
    }

    const response = await axios.post(
      `${process.env.ML_MODELS_ENDPOINT}/api/file-extraction/components`,
      formData,
      {
        headers,
      }
    )

    const components = []
    response.data.components.forEach((component) => {
      components.push({
        componentId: component.component_id,
        componentName: component.component_name,
        nodes: component.nodes.map((node) => {
          return {
            id: node.node_id,
            name: node.name,
            component: node.component_name,
            description: node.description,
            nodeType: node.node_type,
            location: {
              city: node.location?.city,
              country: node.location?.country,
            },
            scrapRate: node.scrap_rate ?? 0,
            scrapFate: node.scrap_fate,
            amount: node.amount,
            quantity: node.quantity,
            unit: node.unit,
          }
        }),
        edges: component.edges.map((edge) => {
          return {
            fromNodeId: edge.from_node_id,
            toNodeId: edge.to_node_id,
          }
        }),
      })
    })

    return {
      components: components,
      warnings: response.data.warnings ?? [],
    }
  } catch (error) {
    let errorMessage = 'Error reading file. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    Sentry.captureException(error)
    throw new GraphQLError(errorMessage)
  }
}
