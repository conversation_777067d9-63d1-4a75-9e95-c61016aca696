import { context } from '@redwoodjs/graphql-server'
import { UpdateUserMetadata } from 'src/lib/auth'
import { GraphQLError } from 'graphql'

export const setDefaultImpactFactor = async ({ categoryName, indicator, lciaMethod, unit }) => {
  try {

    const { urlSafeOrgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await UpdateUserMetadata(
      context.currentUser.userId,
      'defaultImpactFactor',
      {
        ...context.currentUser.metadata?.defaultImpactFactor,
        [tenantID]: {
          categoryName: categoryName,
          indicator: indicator,
          lciaMethod: lciaMethod,
          unit: unit,
        },
      }
    )

    return response
  } catch (error) {
    console.error(error)
    let errorMessage = 'Error updating default impact factor'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
