export const CREATE_PRODUCT_MUTATION = gql`
  mutation CreateProduct($product: CreateProductInput!) {
    createProduct(product: $product) {
      productId
      productName
    }
  }
`

export const CREATE_SUPPLIER_MUTATION = gql`
  mutation CreateSupplier($supplier: CreateSupplierInput!) {
    createSupplier(supplier: $supplier) {
      id
      supplierName
      city
      country
      latitude
      longitude
    }
  }
`
export const CREATE_EMISSIONS_FACTOR_MUTATION = gql`
  mutation CreateEmissionsFactor(
    $emissionsFactor: CreateEmissionsFactorInput!
  ) {
    createEmissionsFactor(emissionsFactor: $emissionsFactor) {
      activityName
      referenceProduct
      description
      geography
      source
      kgCO2e
      unit
    }
  }
`

export const DELETE_PRODUCT_MUTATION = gql`
  mutation DeleteProduct($productId: String!) {
    deleteProduct(productId: $productId)
  }
`

export const BACKUP_PRODUCT_MUTATION = gql`
  mutation BackupProduct($productId: String!) {
    backupProduct(productId: $productId)
  }
`

export const RESTORE_PRODUCT_MUTATION = gql`
  mutation RestoreProduct($productId: String!) {
    restoreProduct(productId: $productId)
  }
`

export const CREATE_PRODUCT_PROCESS_MODEL_MUTATION = gql`
  mutation CreateProductProcessModel(
    $productId: String!
    $processModel: CreateProcessModelInput!
  ) {
    createProductProcessModel(
      productId: $productId
      processModel: $processModel
    )
  }
`

export const ACTIVATE_PRODUCT_MUTATION = gql`
  mutation ActivateProduct($productId: String!) {
    activateProduct(productId: $productId) {
      productId
      productName
      status
    }
  }
`

export const PREDICT_EMISSIONS_FACTORS_QUERY = gql`
  query predictEmissionsFactorsQuery(
    $chemicalName: String!
    $productCategory: String
    $casNo: String
    $geography: String
    $geographyModeling: Boolean
    $unit: String
    $lcaLifecycleStage: String
  ) {
    predictEmissionsFactors(
      chemicalName: $chemicalName
      productCategory: $productCategory
      casNo: $casNo
      geography: $geography
      geographyModeling: $geographyModeling
      unit: $unit
      lcaLifecycleStage: $lcaLifecycleStage
    ) {
      matchedActivity {
        activityName
        referenceProduct
        productInformation
        similarity
        curated
        geography
        source
        unit
        kgCO2e
        exchanges {
          exchangeName
          amount
          unit
          inputStream
          exchangeEmissionsFactor {
            activityName
            referenceProduct
            geography
            source
            unit
          }
        }
        modified
        elementalEfValues {
          lciaMethod
          impactCategoryName
          impactCategoryIndicator
          impactCategoryUnit
          amount
        }
      }
      confidence
      explanation
      recommendations {
        activityName
        referenceProduct
        productInformation
        similarity
        curated
        geography
        source
        unit
      }
    }
  }
`
export const CREATE_INTERMEDIATE_EXHANGE_MUTATION = gql`
  mutation CreateIntermediateExchange(
    $intermediateExchange: CreateIntermediateExchangeInput!
  ) {
    createIntermediateExchange(intermediateExchange: $intermediateExchange) {
      id
      exchangeName
      amount
      unit
    }
  }
`

export const UPDATE_INTERMEDIATE_EXHANGE_MUTATION = gql`
  mutation UpdateIntermediateExchange(
    $exchangeId: Int!
    $intermediateExchange: IntermediateExchangeInput!
  ) {
    updateIntermediateExchange(
      exchangeId: $exchangeId
      intermediateExchange: $intermediateExchange
    ) {
      id
      exchangeName
      amount
      unit
    }
  }
`

export const UPDATE_EMISSIONS_FACTOR_MUTATION = gql`
  mutation UpdateEmissionsFactor(
    $emissionsFactorId: Int!
    $emissionsFactor: UpdateEmissionsFactorInput!
  ) {
    updateEmissionsFactor(
      emissionsFactorId: $emissionsFactorId
      emissionsFactor: $emissionsFactor
    ) {
      id
      activityName
      referenceProduct
      description
    }
  }
`

export const DELETE_INTERMEDIATE_EXHANGE_MUTATION = gql`
  mutation DeleteIntermediateExchange($exchangeId: Int!) {
    deleteIntermediateExchange(exchangeId: $exchangeId)
  }
`

export const EF_INTERMEDIATE_EXCHANGES_QUERY = gql`
query EFIntermediateExchangesQuery(
  $activityName: String!
  $referenceProduct: String!
  $geography: String!
  $source: String!
  $sharedScope: Boolean
) {
  getEFIntermediateExchanges(
    activityName: $activityName
    referenceProduct: $referenceProduct
    geography: $geography
    source: $source
    sharedScope: $sharedScope
  ) {
    id
    exchangeName
    amount
    unit
    exchangeEmissionsFactor {
      activityName
      referenceProduct
      geography
      source
      amount
      unit
    }
    parentEmissionsFactor {
      activityName
      referenceProduct
      geography
      source
      amount
      unit
    }
  }
}
`

export const SEARCH_EMISSIONS_FACTORS_QUERY = gql`
query SearchEmissionsFactorsQuery($activityName: String!) {
  searchEmissionsFactors(activityName: $activityName) {
    activityName
    referenceProduct
    geography
    source
    unit
  }
}
`

export const PREDICT_INGREDIENT_SOURCE_QUERY = gql`
  query PredictIngredientSourceQuery(
    $ingredientName: String!
    $country: String!
  ) {
    predictIngredientSource(
      ingredientName: $ingredientName
      country: $country
    ) {
      ingredientName
      hscode
      productCategory
      manufacturingCountry
      countryCode
      country
      countryCapital
      latitude
      longitude
      locallyProcured
    }
  }
`