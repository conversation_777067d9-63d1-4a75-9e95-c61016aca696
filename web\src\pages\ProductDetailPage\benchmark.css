.benchmark-container {
  position: relative;
  width: 100%;
}

.benchmark-labels {
  display: flex;
  justify-content: space-between;
  margin-top: -5px;
  margin-left: 5px;
}

.benchmark-indicator {
  position: absolute;
  top: 50%;
  transform: rotate(45deg) translateY(-250%);
  background-color: white;
  border: 1px solid red;
  font-size: 12px;
}

.benchmark-description {
  margin-top: 15px;
  font-size: 12px;
}

.benchmark-container {
  opacity: 0.5; /* Reduce opacity */
  pointer-events: none; /* Optional: Disable interactions */
  user-select: none; /* Optional: Prevent text selection */
}

.benchmark-container::after {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2em; /* Adjust size as needed */
  color: #000; /* Text color */
  z-index: 1;
  pointer-events: none; /* Avoid blocking interactions below */
}