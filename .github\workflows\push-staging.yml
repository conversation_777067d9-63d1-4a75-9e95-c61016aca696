name: Push Staging Docker image to GHCR

on:
  push:
    branches:
      - main

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: carbonbright/carbonbright-web
  TAG: staging
  AZURE_WEBAPP_NAME: carbonbright-web

jobs:
  build-and-push:
    runs-on: ubuntu-22.04

    permissions:
      contents: read
      packages: write

    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Create .env file
        run: |
          echo "PROPELAUTH_AUTH_URL=${{ secrets.STAGING_PROPELAUTH_AUTH_URL }}" > .env
          echo "PROPELAUTH_API_TOKEN=${{ secrets.STAGING_PROPELAUTH_API_TOKEN }}" >> .env
          echo "PROPELAUTH_VERIFIER_KEY=${{ secrets.STAGING_PROPELAUTH_VERIFIER_KEY }}" >> .env
          echo "LCA_API_ENDPOINT=${{ secrets.STAGING_LCA_API_ENDPOINT }}" >> .env
          echo "MAPBOX_SECRET_TOKEN=${{ secrets.STAGING_MAPBOX_SECRET_TOKEN }}" >> .env
          echo "MAPBOX_ACCOUNT_USERNAME=${{ secrets.STAGING_MAPBOX_ACCOUNT_USERNAME }}" >> .env
          echo "SERVICE_HUB_PORTAL_ID=${{secrets.STAGING_SERVICE_HUB_PORTAL_ID}}" >> .env
          echo "SENTRY_DSN=${{ secrets.STAGING_SENTRY_DSN }}" >> .env

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.TAG }}

  deploy:
    runs-on: ubuntu-22.04

    needs: build-and-push

    steps:
      - name: Deploy to Azure Web App
        uses: azure/webapps-deploy@v2
        with:
          app-name: ${{ env.AZURE_WEBAPP_NAME }}
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE_STAGING }}
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.TAG }}
          slot-name: 'staging'
