export const schema = gql`
  type ProcessModelEmissionsFactor {
    activityName: String
    referenceProduct: String
    geography: String
    source: String
    kgCO2e: Float
  }

  type ProcessModelWalkerEmissionDetail {
    name: String
    amount: Float
    unit: String
    emissionsFactor: ProcessModelEmissionsFactor
    totalEmissions: Float
  }

  type ProcessModelWalkerEmission {
    totalEmissions: Float
    emissions: [ProcessModelWalkerEmissionDetail]
  }

  type ProcessModelWalker {
    materials: ProcessModelWalkerEmission
    production: ProcessModelWalkerEmission
    transportation: ProcessModelWalkerEmission
    use: ProcessModelWalkerEmission
    eol: ProcessModelWalkerEmission
  }

  type ProcessModelEdges {
    productId: String!
    fromNodeId: Int!
    toNodeId: Int!
  }

  type ProcessModelLocation {
    id: Int
    address1: String
    address2: String
    latitude: Float
    longitude: Float
    city: String
    stateOrProvince: String
    postalCode: String
    country: String
  }

  type ProcessModelNode {
    id: Int
    productId: String
    name: String
    nodeType: String
    amount: Float
    unit: String
    quantity: Int
    location: ProcessModelLocation
    emissionsFactor: ProcessModelEmissionsFactor
  }

  type ProductProcessModel {
    nodes: [ProcessModelNode]!
    edges: [ProcessModelEdges]!
    emissions: ProcessModelWalker
  }

  type Query {
    getProductProcessModel(productId: String!): ProductProcessModel @requireAuth
  }
`
