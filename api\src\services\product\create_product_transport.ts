import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const createProductTransport = async ({
  productId,
  transportSegment,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    await axios.post(
      `${
        process.env.LCA_API_ENDPOINT
      }/product-transport/${tenantID}/${sanitizeInput(productId)}`,
      sanitizeInput(transportSegment)
    )

    return true
  } catch (error) {
    let errorMessage = 'Error creating transport segment. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
