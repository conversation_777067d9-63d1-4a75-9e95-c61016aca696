import { test, expect } from '@playwright/test'
import { CONST } from '../const'
const fs = require('fs')
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
})

test.use({ storageState: CONST.authFile })

test('Create New Default Attribute', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially('Vibrant Colors Laundry Plus', { delay: 100 })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible()

  await productInfo.locator('td:nth-child(3)').click()

  await page.waitForSelector(`text=Vibrant Colors Laundry Plus`)

  await page.getByRole('tab', { name: 'Product Details' }).click()

  await page.locator('#update-default-product-attribute-el').click()

  await page.getByRole('button', { name: 'Add New' }).click()

  await page.waitForSelector(`text=Customize Additional Information`)

  await page.locator('#key').fill('Test Attribute')

  await page.getByRole('button', { name: 'check Save' }).click()

  await page.waitForSelector(`text=New attribute added successfully`)

  await page.waitForSelector(`text=Test Attribute`)
})

test('Update Default Attribute', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially('Vibrant Colors Laundry Plus', { delay: 100 })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible()

  await productInfo.locator('td:nth-child(3)').click()

  await page.waitForSelector(`text=Vibrant Colors Laundry Plus`)

  await page.waitForTimeout(5000)

  await page.getByRole('tab', { name: 'Product Details' }).click()

  await page.locator('#update-default-product-attribute-el').click()

  await page
    .getByRole('row', { name: 'Test Attribute edit Edit' })
    .getByRole('button')
    .click()

  await page.waitForSelector(`text=Customize Additional Information`)

  await page.locator('#key').fill('Test Attribute 101')

  await page.getByRole('button', { name: 'check Save' }).click()

  await page.waitForSelector(`text=Attribute updated successfully`)

  await page.waitForSelector(`text=Test Attribute 101`)
})

test('Update Product Attribute', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially('Vibrant Colors Laundry Plus', { delay: 100 })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible()

  await productInfo.locator('td:nth-child(3)').click()

  await page.waitForSelector(`text=Vibrant Colors Laundry Plus`)

  await page.waitForTimeout(10000)

  await page.getByRole('tab', { name: 'Product Details' }).click()

  await page
    .getByRole('button', { name: 'right Additional Information' })
    .click()

  await page
    .getByRole('row', { name: 'Test Attribute 101 N/A edit' })
    .getByRole('button')
    .click()

  await page.locator('#value').fill('Test Attribute 12345')

  await page.getByRole('button', { name: 'check Save' }).click()

  await page.waitForSelector(`text=Additional information updated successfully`)

  await page.waitForSelector(`text=Test Attribute 12345`)
})
