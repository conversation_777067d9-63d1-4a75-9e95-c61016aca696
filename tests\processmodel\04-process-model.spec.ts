import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true
  }
})

test.use({ storageState: CONST.authFile })

function escapeCSS(str) {
  return str.replace(/[!"#$%&'()*+,.\/:;<=>?@[\\\]^`{|}~]/g, '\\$&');
}

test('Process Model View', async ({ page }) => {
  test.setTimeout(220000)

  page.on('response', async (response) => {
    if (response.url().includes('/graphql')) {
      try {
        const responseBody = await response.json()
        console.log('GraphQL Response:', JSON.stringify(responseBody))
      } catch (error) {
        console.error('Error parsing response:', error)
      }
    }
  })

  await page.goto(`${CONST.baseURL}/products`)

  await page.screenshot({
    path: 'tests/screenshots/process_model_products_page.png',
  })

  await page.getByRole('cell', { name: 'Acme Office Chair', exact: true }).click();
  await page.getByRole('tab', { name: 'Process Model' }).click();

  await page.evaluate(() => {
    document.getElementById('material-Wood-(Oak-Wood)').click();
  });

  await page.waitForSelector(
    `text=Node Details: Wood (Oak Wood)`,
    { timeout: 10000 }
  )

  //verify if material Chloropropyl (Trimethoxysilane) has customized activity match
  //assert that the node details content does not contain "(customized for GLO)"
  await page.evaluate(() => {
    document.getElementById('material-Chloropropyl-(Trimethoxysilane)').click();
  });

  await page.waitForSelector(
    `text=Node Details: Chloropropyl (Trimethoxysilane)`,
    { timeout: 10000 }
  )
  // grab the node details drawer content
  const drawerContent = await page.content();
  expect(drawerContent).not.toContain("(customized for GLO)")


  await page.evaluate(() => {
    document.getElementById('production-Cutting').click();
  });

  await page.waitForSelector(
    `text=Node Details: Cutting`,
    { timeout: 10000 }
  )


  await page.evaluate(() => {
    document.getElementById('bundle-Product-Assembly').click();
  });

  await page.waitForSelector(
    `text=Node Details: Product Assembly`,
    { timeout: 10000 }
  )


  await page.evaluate(() => {
    document.getElementById('use-Consumer-Use').click();
  });

  await page.waitForSelector(
    `text=Node Details: Consumer Use`,
    { timeout: 10000 }
  )

})