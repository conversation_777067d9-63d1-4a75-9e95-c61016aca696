export const getMapboxAccessTokenFromCache = () => {
  const mapboxAccessToken = localStorage.getItem('mapboxAccessToken')

  if (!mapboxAccessToken) {
    return null
  }

  try {
    const mapboxAccessTokenData = JSON.parse(mapboxAccessToken)

    if (mapboxAccessTokenData.expiresAt < new Date().toISOString()) {
      return null
    }

    return mapboxAccessTokenData
  } catch (error) {
    return console.error('Error parsing mapboxAccessToken:', error)
  }
}

export const setMapboxAccessTokenToCache = (mapboxAccessTokenData) => {
  localStorage.setItem(
    'mapboxAccessToken',
    JSON.stringify(mapboxAccessTokenData)
  )
}