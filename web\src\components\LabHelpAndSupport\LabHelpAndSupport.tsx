import React, { useState } from 'react'
import {
  Drawer,
  Avatar,
  Form,
  Input,
  Button,
  message,
  Card,
  Space,
  Collapse,
} from 'antd'
import {
  QuestionCircleOutlined,
  ArrowLeftOutlined,
  PictureOutlined,
  BulbOutlined,
  ToolOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
  CustomerServiceOutlined,
  MessageOutlined,
} from '@ant-design/icons'
import { useMutation } from '@redwoodjs/web'
import html2canvas from 'html2canvas'
import Title from 'antd/es/typography/Title'
import Paragraph from 'antd/es/typography/Paragraph'
const { Panel } = Collapse

const { TextArea } = Input

const CONTACT_SUPPORT_MUTATION = gql`
  mutation ContactSupport($supportRequest: SupportRequestInput!) {
    contactSupport(supportRequest: $supportRequest)
  }
`

const SUBMIT_FEEDBACK_MUTATION = gql`
  mutation SubmitFeedback($feedbackRequest: FeedbackRequestInput!) {
    submitFeedback(feedbackRequest: $feedbackRequest)
  }
`

const LabHelpAndSupport = ({ userMetadata }) => {
  const [contactSupportDrawerVisible, setContactSupportDrawerVisible] =
    useState(false)
  const [feedbackDrawerVisible, setFeedbackDrawerVisible] = useState(false)
  const [contactSupportScreenshotImage, setContactSupportScreenshotImage] =
    useState(null)
  const [feedbackScreenshotImage, setFeedbackScreenshotImage] = useState(null)
  const [contactSupportForm] = Form.useForm()
  const [feedbackForm] = Form.useForm()

  const [contactSupport, { loading: contactSupportIsLoading }] = useMutation(
    CONTACT_SUPPORT_MUTATION
  )
  const [submitFeedback, { loading: submitFeedbackIsLoading }] = useMutation(
    SUBMIT_FEEDBACK_MUTATION
  )

  const onContactSupportDrawerClose = () => {
    contactSupportForm.resetFields()
    setContactSupportDrawerVisible(false)
  }
  const onFeedbackDrawerClose = () => {
    feedbackForm.resetFields()
    setFeedbackDrawerVisible(false)
  }

  const takeSupportScreenshot = () => {
    html2canvas(document.querySelector('#redwood-app'))
      .then((canvas) => {
        const imgData = canvas.toDataURL('image/png')
        setContactSupportScreenshotImage(imgData)
        message.success(
          'Screenshot taken successfully. Please describe your issue and click submit.'
        )
      })
      .catch((err) => {
        console.error('Error taking screenshot:', err)
        message.error('Failed to take screenshot. Please try again.')
      })
  }

  const takeFeedbackScreenshot = () => {
    html2canvas(document.querySelector('#redwood-app'))
      .then((canvas) => {
        const imgData = canvas.toDataURL('image/png')
        setFeedbackScreenshotImage(imgData)
        message.success(
          'Screenshot taken successfully. Please provide your feedback and click submit.'
        )
      })
      .catch((err) => {
        console.error('Error taking screenshot:', err)
        message.error('Failed to take screenshot. Please try again.')
      })
  }

  const handleContactSupport = async (values) => {
    try {
      const supportRequest = {
        name:
          values.name ??
          userMetadata.user.firstName + ' ' + userMetadata.user.lastName,
        email: values.email,
        subject: values.subject ?? 'Support Request',
        description: values.description,
        screenshot: contactSupportScreenshotImage,
      }
      await contactSupport({ variables: { supportRequest } })
      message.success(
        'Support request submitted successfully. We will get back to you soon.'
      )
      setContactSupportDrawerVisible(false)
    } catch (error) {
      message.error('Failed to submit support request. Please try again.')
    }
  }

  const handleSubmitFeedback = async (values) => {
    try {
      const feedbackRequest = {
        name:
          values.name ??
          userMetadata.user.firstName + ' ' + userMetadata.user.lastName,
        email: values.email,
        subject: values.subject ?? 'Feedback/Suggestions',
        feedback: values.feedback,
        screenshot: feedbackScreenshotImage,
      }
      await submitFeedback({ variables: { feedbackRequest } })
      message.success(
        'Feedback submitted successfully. We appreciate your feedback.'
      )
      setFeedbackDrawerVisible(false)
    } catch (error) {
      message.error('Failed to submit support request. Please try again.')
    }
  }

  return (
    <>
      <Card
        title={
          <Space>
            <BulbOutlined style={{ fontSize: '24px', color: '#faad14' }} />
            <Title level={3} style={{ margin: 0 }}>
              Help Center
            </Title>
          </Space>
        }
      >
        <Collapse defaultActiveKey={['1']} expandIconPosition="right">
          <Panel
            header={
              <Space>
                <ToolOutlined />
                How to Use the AI Emission Factor Matching Tool
              </Space>
            }
            key="1"
          >
            <Paragraph>
              Follow these steps to get the most out of our AI Emission Factor Matching Tool:
            </Paragraph>
            <ol>
              <li>
                Enter your material or process in the search bar (e.g., "steel
                production").
              </li>
              <li>
                Be as specific as possible, including production methods if
                known.
              </li>
              <li>Click the "Search" button or press Enter to search.</li>
              <li>Review the results, which show the matched acitivity.</li>
            </ol>
          </Panel>
          <Panel
            header={
              <Space>
                <InfoCircleOutlined />
                Tips for Accurate Matching
              </Space>
            }
            key="2"
          >
            <ul>
              <li>Use industry-standard terminology for best results.</li>
              <li>
                Include the production method (e.g., "steel production blast
                furnace" instead of just "steel").
              </li>
              <li>
                Specify the material grade or quality if relevant (e.g.,
                "high-grade aluminum").
              </li>
              <li>
                If you can't find an exact match, try broader categories and
                refine from there.
              </li>
              <li>
                Consider regional variations and specify the region if known
                (e.g., "European steel production").
              </li>
            </ul>
          </Panel>
          <Panel
            header={
              <Space>
                <FileTextOutlined />
                Understanding Emissions Factors
              </Space>
            }
            key="3"
          >
            <Paragraph>
              Emissions factors (EF) represent the average emission rate of a
              given pollutant for a specific activity or process. They are
              expressed in units of weight (e.g., kg CO2e) per unit of activity
              (e.g., per kg of material produced).
            </Paragraph>
            <Paragraph>Key points to remember:</Paragraph>
            <ul>
              <li>
                EFs can vary based on production methods, technologies, and
                regions.
              </li>
              <li>
                They typically include multiple greenhouse gases converted to
                CO2 equivalent (CO2e).
              </li>
              <li>
                EFs may be updated periodically as production processes and data
                improve.
              </li>
              <li>
                When using EFs, consider the scope (e.g., cradle-to-gate,
                cradle-to-grave) for accurate comparisons.
              </li>
            </ul>
          </Panel>
          <Panel
            header={
              <Space>
                <QuestionCircleOutlined />
                Frequently Asked Questions
              </Space>
            }
            key="4"
          >
            <Collapse ghost>
              <Panel
                header="What are emission factor databases that you support in this tool?"
                key="1"
              >
                <Paragraph>
                  Currently we support Ecoinvent, DEFRA and lookups on subset of
                  our our proprietary database
                </Paragraph>
              </Panel>
              <Panel
                header="Can we get access to the emission factor values?"
                key="2"
              >
                <Paragraph>
                  Please get in touch with us to using "Contact Support" get
                  discuss access to the emission factor values
                </Paragraph>
              </Panel>
              <Panel
                header="What if I can't find my specific material?"
                key="3"
              >
                <Paragraph>
                  If you can't find your specific material, try these steps:
                  <ol>
                    <li>Use a more general category of the material.</li>
                    <li>
                      Check for alternative names or industry-specific
                      terminology.
                    </li>
                    <li>
                      Contact our support team for assistance in finding the
                      closest match.
                    </li>
                  </ol>
                </Paragraph>
              </Panel>
              <Panel
                header="How often are the emissions factors updated?"
                key="4"
              >
                <Paragraph>
                  We update our emissions factor database regularly. However,
                  significant changes in industry standards or new research
                  findings may prompt more frequent updates for specific
                  materials or processes.
                </Paragraph>
              </Panel>
              <Panel
                header="Can I suggest new materials to be added to the database?"
                key="5"
              >
                <Paragraph>
                  Yes! We welcome suggestions for new materials or processes.
                  Please use the "Send Feedback" button below to write to us
                  with your suggestions. Include as much detail as possible
                  about the material or process you'd like to see added.
                </Paragraph>
              </Panel>
            </Collapse>
          </Panel>
        </Collapse>
        <br />
        <Space
          direction="vertical"
          style={{ width: '100%', marginTop: '20px' }}
        >
          <Button
            type="primary"
            icon={<CustomerServiceOutlined />}
            block
            onClick={() => {
              setContactSupportDrawerVisible(true)
            }}
          >
            Contact Support
          </Button>
          <Button
            icon={<MessageOutlined />}
            block
            onClick={() => {
              setFeedbackDrawerVisible(true)
            }}
          >
            Send Feedback
          </Button>
        </Space>
      </Card>
      <Drawer
        title="Contact Support"
        closeIcon={<ArrowLeftOutlined />}
        placement="right"
        onClose={onContactSupportDrawerClose}
        open={contactSupportDrawerVisible}
      >
        <Form
          form={contactSupportForm}
          name="basic"
          layout="vertical"
          initialValues={{
            name:
              userMetadata?.user?.firstName +
              ' ' +
              userMetadata?.user?.lastName,
            email: userMetadata?.user?.email,
          }}
          autoComplete="off"
          style={{
            marginTop: '25px',
          }}
          onFinish={handleContactSupport}
        >
          <Form.Item
            name="name"
            label="Your Name"
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              {
                required: true,
                message: 'Please enter a valid email address',
              },
            ]}
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="subject"
            label="Subject"
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="description"
            label="Description"
            rules={[
              {
                required: true,
                message: 'Please enter a valid description',
              },
            ]}
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <TextArea rows={4} style={{ fontWeight: 'normal' }} />
          </Form.Item>

          <Form.Item>
            <Button
              style={{
                width: '100%',
                height: 40,
                backgroundColor: '#f6f6f6',
              }}
              type="default"
              icon={<PictureOutlined />}
              onClick={takeSupportScreenshot}
            >
              Take screenshot
            </Button>
            <p
              style={{
                display: contactSupportScreenshotImage
                  ? 'inline-block'
                  : 'none',
              }}
            >
              Screenshot attached
            </p>
          </Form.Item>

          <Form.Item>
            <Button
              loading={contactSupportIsLoading}
              htmlType="submit"
              style={{
                width: '100%',
                height: 40,
                backgroundColor: 'rgb(78 184 88)',
                color: 'white',
                fontWeight: 'bold',
              }}
              type="default"
            >
              Submit
            </Button>
          </Form.Item>
        </Form>
      </Drawer>
      <Drawer
        title="Send Feedback"
        closeIcon={<ArrowLeftOutlined />}
        placement="right"
        onClose={onFeedbackDrawerClose}
        open={feedbackDrawerVisible}
      >
        <Form
          form={feedbackForm}
          name="basic"
          layout="vertical"
          initialValues={{
            name:
              userMetadata?.user?.firstName +
              ' ' +
              userMetadata?.user?.lastName,
            email: userMetadata?.user?.email,
          }}
          autoComplete="off"
          style={{
            marginTop: '25px',
          }}
          onFinish={handleSubmitFeedback}
        >
          <Form.Item
            name="name"
            label="Your Name"
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              {
                required: true,
                message: 'Please enter a valid email address',
              },
            ]}
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="subject"
            label="Subject"
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="feedback"
            label="Feedback/Suggestions"
            rules={[
              {
                required: true,
                message: 'Please enter your feedback/suggestions',
              },
            ]}
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <TextArea rows={4} style={{ fontWeight: 'normal' }} />
          </Form.Item>

          <Form.Item>
            <Button
              style={{
                width: '100%',
                height: 40,
                backgroundColor: '#f6f6f6',
              }}
              type="default"
              icon={<PictureOutlined />}
              onClick={takeFeedbackScreenshot}
            >
              Take screenshot
            </Button>
            <p
              style={{
                display: feedbackScreenshotImage ? 'inline-block' : 'none',
              }}
            >
              Screenshot attached
            </p>
          </Form.Item>

          <Form.Item>
            <Button
              loading={submitFeedbackIsLoading}
              htmlType="submit"
              style={{
                width: '100%',
                height: 40,
                backgroundColor: 'rgb(78 184 88)',
                color: 'white',
                fontWeight: 'bold',
              }}
              type="default"
            >
              Submit
            </Button>
          </Form.Item>
        </Form>
      </Drawer>
    </>
  )
}

export default LabHelpAndSupport
