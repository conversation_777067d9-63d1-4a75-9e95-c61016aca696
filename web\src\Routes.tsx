// In this file, all Page components from 'src/pages` are auto-imported. Nested
// directories are supported, and should be uppercase. Each subdirectory will be
// prepended onto the component name.
//
// Examples:
//
// 'src/pages/HomePage/HomePage.js'         -> HomePage
// 'src/pages/Admin/BooksPage/BooksPage.js' -> AdminBooksPage

import { Router, Route, Set, Private } from '@redwoodjs/router'

import { useAuth } from './auth'
import MainLayout from './layouts/MainLayout/MainLayout'
import LabLayout from './layouts/LabLayout/LabLayout'

const Routes = () => {
  return (
    <Router useAuth={useAuth}>
      <Route path="/login" page={LogInPage} name="logIn" />
      <Route path="/help-and-support" page={HelpAndSupportPage} name="helpAndSupport" />

      <Set wrap={MainLayout}>
        <Private unauthenticated="logIn">
          <Route path="/" page={HomePage} name="home" />
          <Route path="/dashboard" page={DashboardPage} name="dashboard" />
          <Route path="/products" page={InventoryPage} name="products" />
          <Route path="/component" page={ComponentPage} name="component" />
          <Route path="/datasets" page={ActivityDatasetsPage} name="activityDatasets" />
          <Route path="/product/add" page={AddProductPage} name="addProduct" />
          <Route path="/product/import" page={AddProductImportFilePage} name="addProductImportFile" />
          <Route path="/component/import" page={AddComponentImportFilePage} name="addComponentImportFile" />
          <Route path="/product/{productId}/edit/{step}" page={EditProductPage} name="editProduct" />
          <Route path="/{tenantID}/product-detail/{productId}" page={ProductDetailPage} name="productDetail" />
          <Route path="/{tenantID}/component-detail/{productId}" page={ComponentDetailPage} name="componentDetail" />
          <Route path="/compare-product/{productId}/{clonedProductId}" page={CompareProductPage} name="compareProduct" />
          <Route path="/reports" page={ReportsPage} name="reports" />
          <Route path="/suppliers" page={SupplierPage} name="suppliers" />
          <Route path="/supplier-info/{supplierId}" page={SupplierInfoPage} name="supplierInfo" />
          <Route path="/reports/inventory-summary" page={ReportsInventorySummaryPage} name="reportsInventorySummary" />
          <Route path="/reports/emissions-by-category" page={ReportsCategoryEmissionsPage} name="reportsCategoryEmissions" />
          <Route path="/reports/emissions-by-supplier" page={ReportsSupplierEmissionsPage} name="reportsSupplierEmissions" />
          <Route path="/recommendations" page={RecommendationsPage} name="recommendations" />
          <Route path="/product-sustainability-widget" page={ProductSustainabilityWidgetPage} name="productSustainabilityWidget" />
        </Private>
      </Set>
      <Set wrap={LabLayout}>
        <Private unauthenticated="logIn">
          <Route path="/labs" page={LabHomePage} name="labHome" />
          <Route path="/labs/ef-matching" page={LabEFMatchingPage} name="labEfMatching" />
        </Private>
      </Set>
      <Route notfound page={NotFoundPage} />
    </Router>
  )
}

export default Routes
