import { context } from '@redwoodjs/graphql-server'
import { UpdateOrgMetadata } from 'src/lib/auth'
import { GraphQLError } from 'graphql'

export const upgradeTrial = async () => {
  try {
    const response = await UpdateOrgMetadata(
      context.currentUser.orgMemberInfo.orgId,
      'upgradeRequested',
      true
    )

    return response
  } catch (error) {
    console.error(error)
    let errorMessage = 'Error requesting trial upgrade. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
