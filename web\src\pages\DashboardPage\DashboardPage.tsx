import { MetaTags } from '@redwoodjs/web'
import { Layout, Tabs } from 'antd'
import type { TabsProps } from 'antd'
import { useState } from 'react'
const { Content } = Layout
import DashboardCell from 'src/components/DashboardCell'
import GettingStartedCell from 'src/components/GettingStartedCell'

const DashboardPage = () => {

  return (
    <>
      <MetaTags
        title="Product Emissions Dashboard"
        description="Product Emissions Dashboard"
      />
      <Content>
        <Layout>
          <Content>
            <DashboardCell />
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default DashboardPage
