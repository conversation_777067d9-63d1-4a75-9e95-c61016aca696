import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import axiosRetry from 'axios-retry'
import { GraphQLError } from 'graphql'

axiosRetry(axios, {
  retries: 3,
  retryDelay: axiosRetry.exponentialDelay,
})

export const predictManufacturingProcesses = async ({
  productName,
  productCategory,
}) => {
  try {
    const response = await axios.get(
      `${
        process.env.ML_MODELS_ENDPOINT
      }/api/product-manufacturing/manufacturing-processes/${encodeURIComponent(
        sanitizeInput(productCategory)
      )}/${encodeURIComponent(sanitizeInput(productName))}`
    )

    const manufacturingProcesses = response.data

    return manufacturingProcesses
  } catch (error) {
    let errorMessage =
      'Error predicting manufacturing processes. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
