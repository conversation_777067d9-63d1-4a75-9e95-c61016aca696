{"compilerOptions": {"noEmit": true, "allowJs": true, "esModuleInterop": true, "target": "esnext", "module": "esnext", "moduleResolution": "node", "skipLibCheck": false, "baseUrl": "./", "rootDirs": ["./src", "../.redwood/types/mirror/api/src"], "paths": {"src/*": ["./src/*", "../.redwood/types/mirror/api/src/*"], "types/*": ["./types/*", "../types/*"], "@redwoodjs/testing": ["../node_modules/@redwoodjs/testing/api"]}, "typeRoots": ["../node_modules/@types", "./node_modules/@types"], "types": ["jest"], "jsx": "react-jsx"}, "include": ["src", "../.redwood/types/includes/all-*", "../.redwood/types/includes/api-*", "../types"]}