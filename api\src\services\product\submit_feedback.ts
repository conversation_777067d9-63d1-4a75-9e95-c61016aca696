import nodemailer from 'nodemailer'
import { GraphQLError } from 'graphql'

export const submitFeedback = async ({ feedbackRequest }) => {
  try {
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.FEEDBACK_EMAIL,
        pass: process.env.FEEDBACK_PASSWORD,
      },
    })

    let mimeType = null,
      base64Data = null

    if (feedbackRequest.screenshot) {
      const base64SegmentData = feedbackRequest.screenshot.match(
        /^data:(.+);base64,(.+)$/
      )

      if (base64SegmentData.length == 3) {
        mimeType = base64SegmentData[1]
        base64Data = base64SegmentData[2]
      }
    }

    let mailOptions = {
      from: process.env.FEEDBACK_EMAIL,
      to: process.env.FEEDBACK_RECIPIENTS,
      subject: `Feedback: ` + feedbackRequest.subject,
      text: `User Name: ${feedbackRequest.name}\nUser Email: ${feedbackRequest.email}\n\n${feedbackRequest.feedback}`,
    }

    if (mimeType && base64Data) {
      mailOptions['attachments'] = [
        {
          filename: 'screenshot.png',
          content: Buffer.from(base64Data, 'base64'),
          encoding: 'base64',
          contentType: mimeType,
        },
      ]
    }

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.log(error)
        throw new Error(error)
      }

      console.log('Feedback Sent: ' + info.response)
      return true
    })
  } catch (error) {
    let errorMessage = 'Error sending feedback. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
