import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'


export const createProductManufacturing = async ({
  productId,
  manufacturingProcess,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    await axios.post(
      `${process.env.LCA_API_ENDPOINT}/manufacturing-processes/${tenantID}/${sanitizeInput(productId)}`,
      {
        processes: sanitizeInput(manufacturingProcess),
      }
    )

    return true

  } catch (error) {
    let errorMessage = 'Error creating manufacturing processes. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
