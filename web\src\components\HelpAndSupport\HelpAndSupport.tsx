import React, { useState } from 'react'
import { navigate, routes } from '@redwoodjs/router'
import {
  Drawer,
  List,
  Divider,
  Avatar,
  Form,
  Input,
  Button,
  message,
} from 'antd'
import {
  SendOutlined,
  QuestionCircleOutlined,
  CommentOutlined,
  InfoCircleOutlined,
  InfoOutlined,
  UnorderedListOutlined,
  ArrowLeftOutlined,
  PictureOutlined,
} from '@ant-design/icons'
import { useMutation } from '@redwoodjs/web'
import html2canvas from 'html2canvas'

const { TextArea } = Input

const CONTACT_SUPPORT_MUTATION = gql`
  mutation ContactSupport($supportRequest: SupportRequestInput!) {
    contactSupport(supportRequest: $supportRequest)
  }
`

const SUBMIT_FEEDBACK_MUTATION = gql`
  mutation SubmitFeedback($feedbackRequest: FeedbackRequestInput!) {
    submitFeedback(feedbackRequest: $feedbackRequest)
  }
`

const LOG_PRODUCT_TOUR_STATUS = gql`
  mutation LogProductTourStatus($tourCompleted: Boolean!) {
    logProductTourStatus(tourCompleted: $tourCompleted)
  }
`

const HelpAndSupport = ({ userMetadata }) => {
  const [helpDrawerVisible, setHelpDrawerVisible] = useState(false)
  const [contactSupportDrawerVisible, setContactSupportDrawerVisible] =
    useState(false)
  const [feedbackDrawerVisible, setFeedbackDrawerVisible] = useState(false)
  const [aboutDrawerVisible, setAboutDrawerVisible] = useState(false)
  const [tosDrawerVisible, setTosDrawerVisible] = useState(false)
  const [contactSupportScreenshotImage, setContactSupportScreenshotImage] =
    useState(null)
  const [feedbackScreenshotImage, setFeedbackScreenshotImage] = useState(null)
  const [contactSupportForm] = Form.useForm()
  const [feedbackForm] = Form.useForm()

  const [contactSupport, { loading: contactSupportIsLoading }] = useMutation(
    CONTACT_SUPPORT_MUTATION
  )
  const [submitFeedback, { loading: submitFeedbackIsLoading }] = useMutation(
    SUBMIT_FEEDBACK_MUTATION
  )

  const [logProductTourStatus] = useMutation(LOG_PRODUCT_TOUR_STATUS)

  const onHelpDrawerClose = () => {
    setHelpDrawerVisible(false)
  }

  const onContactSupportDrawerClose = () => {
    setContactSupportDrawerVisible(false)
    setHelpDrawerVisible(true)
  }

  const onFeedbackDrawerClose = () => {
    setFeedbackDrawerVisible(false)
    setHelpDrawerVisible(true)
  }

  const onAboutDrawerClose = () => {
    setAboutDrawerVisible(false)
    setHelpDrawerVisible(true)
  }

  const onTosDrawerClose = () => {
    setTosDrawerVisible(false)
    setHelpDrawerVisible(true)
  }

  const resetProductTourStatus = async () => {
    await logProductTourStatus({
      variables: { tourCompleted: false },
    })
  }

  const takeSupportScreenshot = () => {
    html2canvas(document.querySelector('#redwood-app'))
      .then((canvas) => {
        const imgData = canvas.toDataURL('image/png')
        setContactSupportScreenshotImage(imgData)
        message.success(
          'Screenshot taken successfully. Please describe your issue and click submit.'
        )
      })
      .catch((err) => {
        console.error('Error taking screenshot:', err)
        message.error('Failed to take screenshot. Please try again.')
      })
  }

  const takeFeedbackScreenshot = () => {
    html2canvas(document.querySelector('#redwood-app'))
      .then((canvas) => {
        const imgData = canvas.toDataURL('image/png')
        setFeedbackScreenshotImage(imgData)
        message.success(
          'Screenshot taken successfully. Please provide your feedback and click submit.'
        )
      })
      .catch((err) => {
        console.error('Error taking screenshot:', err)
        message.error('Failed to take screenshot. Please try again.')
      })
  }

  const handleContactSupport = async (values) => {
    try {
      const supportRequest = {
        name:
          values.name ??
          userMetadata.user.firstName + ' ' + userMetadata.user.lastName,
        email: values.email,
        subject: values.subject ?? 'Support Request',
        description: values.description,
        screenshot: contactSupportScreenshotImage,
      }
      await contactSupport({ variables: { supportRequest } })
      message.success(
        'Support request submitted successfully. We will get back to you soon.'
      )
      setContactSupportDrawerVisible(false)
    } catch (error) {
      message.error('Failed to submit support request. Please try again.')
    }
  }

  const handleSubmitFeedback = async (values) => {
    try {
      const feedbackRequest = {
        name:
          values.name ??
          userMetadata.user.firstName + ' ' + userMetadata.user.lastName,
        email: values.email,
        subject: values.subject ?? 'Feedback/Suggestions',
        feedback: values.feedback,
        screenshot: feedbackScreenshotImage,
      }
      await submitFeedback({ variables: { feedbackRequest } })
      message.success(
        'Feedback submitted successfully. We appreciate your feedback.'
      )
      setFeedbackDrawerVisible(false)
    } catch (error) {
      message.error('Failed to submit support request. Please try again.')
    }
  }

  const handleHelpMenuClick = async (title) => {
    switch (title) {
      case 'Product Tour':
        setHelpDrawerVisible(false)
        await resetProductTourStatus()
        window.location.href = '/'
        break
      case 'Contact Support':
        contactSupportForm.resetFields()
        setContactSupportScreenshotImage(null)
        setContactSupportDrawerVisible(true)
        setHelpDrawerVisible(false)
        break
      case 'Feedback':
        feedbackForm.resetFields()
        setFeedbackScreenshotImage(null)
        setFeedbackDrawerVisible(true)
        setHelpDrawerVisible(false)
        break
      case 'Help Center':
        window.open(
          'https://rounded-lyre-c7b.notion.site/Getting-Started-Your-First-Product-LCA-579ed302f09e47318ab8f551bb33cac4',
          '_blank'
        )
        break
      case 'About':
        setAboutDrawerVisible(true)
        setHelpDrawerVisible(false)
        break
      case 'Terms of Service':
        setTosDrawerVisible(true)
        setHelpDrawerVisible(false)
        break
      default:
        break
    }
  }

  return (
    <>
      <Avatar
        style={{
          backgroundColor: '#FFC000',
          color: 'white',
          cursor: 'pointer',
          marginRight: 16,
        }}
        className="help-and-support-menu"
        icon={<QuestionCircleOutlined />}
        onClick={() => setHelpDrawerVisible(true)}
      />
      <Drawer
        title="Help"
        placement="right"
        onClose={onHelpDrawerClose}
        open={helpDrawerVisible}
      >
        <List
          itemLayout="horizontal"
          dataSource={[
            {
              title: 'Product Tour',
              icon: <SendOutlined />,
              id: 'getting-started-tour-btn',
            },
            {
              title: 'Help Center',
              icon: <QuestionCircleOutlined />,
              id: 'help-center-btn',
            },
            {
              title: 'Contact Support',
              icon: <CommentOutlined />,
              id: 'contact-support-btn',
            },
            {
              title: 'Feedback',
              icon: <InfoCircleOutlined />,
              id: 'feedback-btn',
            },
          ]}
          renderItem={(item) => (
            <List.Item
              id={item.id}
              onClick={() => handleHelpMenuClick(item.title)}
            >
              <List.Item.Meta
                avatar={item.icon}
                title={<p style={{ cursor: 'pointer' }}>{item.title}</p>}
              />
            </List.Item>
          )}
        />
        <Divider style={{ marginTop: '50%' }} />
        <List
          itemLayout="horizontal"
          dataSource={[
            {
              title: 'About',
              icon: <InfoOutlined />,
            },
            {
              title: 'Terms of Service',
              icon: <UnorderedListOutlined />,
            },
          ]}
          renderItem={(item) => (
            <List.Item onClick={() => handleHelpMenuClick(item.title)}>
              <List.Item.Meta
                avatar={item.icon}
                title={<p style={{ cursor: 'pointer' }}>{item.title}</p>}
              />
            </List.Item>
          )}
        />
      </Drawer>
      <Drawer
        title="Contact Support"
        closeIcon={<ArrowLeftOutlined />}
        placement="right"
        onClose={onContactSupportDrawerClose}
        open={contactSupportDrawerVisible}
      >
        <Form
          form={contactSupportForm}
          name="basic"
          layout="vertical"
          initialValues={{
            name:
              userMetadata?.user?.firstName +
              ' ' +
              userMetadata?.user?.lastName,
            email: userMetadata?.user?.email,
          }}
          autoComplete="off"
          style={{
            marginTop: '25px',
          }}
          onFinish={handleContactSupport}
        >
          <Form.Item
            name="name"
            label="Your Name"
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              {
                required: true,
                message: 'Please enter a valid email address',
              },
            ]}
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="subject"
            label="Subject"
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="description"
            label="Description"
            rules={[
              {
                required: true,
                message: 'Please enter a valid description',
              },
            ]}
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <TextArea rows={4} style={{ fontWeight: 'normal' }} />
          </Form.Item>

          <Form.Item>
            <Button
              style={{
                width: '100%',
                height: 40,
                backgroundColor: '#f6f6f6',
              }}
              type="default"
              icon={<PictureOutlined />}
              onClick={takeSupportScreenshot}
            >
              Take screenshot
            </Button>
            <p
              style={{
                display: contactSupportScreenshotImage
                  ? 'inline-block'
                  : 'none',
              }}
            >
              Screenshot attached
            </p>
          </Form.Item>

          <Form.Item>
            <Button
              loading={contactSupportIsLoading}
              htmlType="submit"
              style={{
                width: '100%',
                height: 40,
                backgroundColor: 'rgb(78 184 88)',
                color: 'white',
                fontWeight: 'bold',
              }}
              type="default"
            >
              Submit
            </Button>
          </Form.Item>
        </Form>
      </Drawer>
      <Drawer
        title="Feedback"
        closeIcon={<ArrowLeftOutlined />}
        placement="right"
        onClose={onFeedbackDrawerClose}
        open={feedbackDrawerVisible}
      >
        <Form
          form={feedbackForm}
          name="basic"
          layout="vertical"
          initialValues={{
            name:
              userMetadata?.user?.firstName +
              ' ' +
              userMetadata?.user?.lastName,
            email: userMetadata?.user?.email,
          }}
          autoComplete="off"
          style={{
            marginTop: '25px',
          }}
          onFinish={handleSubmitFeedback}
        >
          <Form.Item
            name="name"
            label="Your Name"
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              {
                required: true,
                message: 'Please enter a valid email address',
              },
            ]}
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="subject"
            label="Subject"
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <Input style={{ fontWeight: 'normal' }} />
          </Form.Item>
          <Form.Item
            name="feedback"
            label="Feedback/Suggestions"
            rules={[
              {
                required: true,
                message: 'Please enter your feedback/suggestions',
              },
            ]}
            style={{ fontWeight: 'normal', color: 'darkgrey' }}
          >
            <TextArea rows={4} style={{ fontWeight: 'normal' }} />
          </Form.Item>

          <Form.Item>
            <Button
              style={{
                width: '100%',
                height: 40,
                backgroundColor: '#f6f6f6',
              }}
              type="default"
              icon={<PictureOutlined />}
              onClick={takeFeedbackScreenshot}
            >
              Take screenshot
            </Button>
            <p
              style={{
                display: feedbackScreenshotImage ? 'inline-block' : 'none',
              }}
            >
              Screenshot attached
            </p>
          </Form.Item>

          <Form.Item>
            <Button
              loading={submitFeedbackIsLoading}
              htmlType="submit"
              style={{
                width: '100%',
                height: 40,
                backgroundColor: 'rgb(78 184 88)',
                color: 'white',
                fontWeight: 'bold',
              }}
              type="default"
            >
              Submit
            </Button>
          </Form.Item>
        </Form>
      </Drawer>
      <Drawer
        title="About"
        closeIcon={<ArrowLeftOutlined />}
        placement="right"
        onClose={onAboutDrawerClose}
        open={aboutDrawerVisible}
      >
        <p>Version</p>
        <p>1.1</p>
        <p style={{ marginTop: 20 }}>
          Welcome to CarbonBright, a pioneering SaaS platform revolutionizing
          sustainability in the CPG industry. Our state-of-the-art AI-driven
          technology simplifies the process of Life Cycle Assessments (LCAs),
          helping companies measure and reduce their environmental impact with
          unprecedented accuracy and efficiency. From instant product-level
          impact metrics to comprehensive Scope 3 GHG analysis, CarbonBright
          empowers businesses to transform their product sustainability, meet
          regulatory requirements, and achieve their supply chain emissions
          reduction goals.
        </p>
      </Drawer>
      <Drawer
        title="Terms of Service"
        closeIcon={<ArrowLeftOutlined />}
        placement="right"
        onClose={onTosDrawerClose}
        open={tosDrawerVisible}
      >
        <p>Last Updated on</p>
        <p>August 1st, 2024</p>
        <p style={{ marginTop: 20 }}>
          By using our software application, you agree to comply with and be
          bound by the following terms and conditions. Please read them
          carefully.
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>1. Acceptance of Terms:</strong> By accessing or using the
          CarbonBright platform, you agree to these Terms of Service and our
          Privacy Policy. If you do not agree with these terms, you must not use
          our services.
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>2. Changes to Terms:</strong> We reserve the right to modify
          these terms at any time. Any changes will be effective immediately
          upon posting on our application. Your continued use of the platform
          constitutes your acceptance of the revised terms.
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>3. User Accounts:</strong> To access certain features, you may
          need to create an account. You are responsible for maintaining the
          confidentiality of your account information and for all activities
          that occur under your account. You agree to notify us immediately of
          any unauthorized use of your account.
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>4. Use of the Platform:</strong> You agree to use CarbonBright
          in compliance with all applicable laws and regulations. You shall not:
          <ul>
            <li>
              &bull; Use the platform for any illegal or unauthorized purpose.
            </li>
            <li>
              &bull; Interfere with or disrupt the integrity or performance of
              the platform.
            </li>
            <li>
              &bull; Attempt to gain unauthorized access to the platform or its
              related systems.
            </li>
          </ul>
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>5. Intellectual Property:</strong> All content, trademarks,
          service marks, logos, and other intellectual property displayed on the
          platform are the property of CarbonBright or its licensors. You are
          granted a limited, non-exclusive, non-transferable license to use the
          platform for its intended purpose.
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>6. Data and Privacy:</strong> We respect your privacy and are
          committed to protecting your personal data. Please review our Privacy
          Policy to understand our practices regarding the collection, use, and
          protection of your information.
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>7. Fees and Payments:</strong> Usage of our platform and
          access to certain features may be subject to licensing fees. You agree
          to pay all applicable fees as described in our pricing plans. All
          payments are non-refundable except as required by law.
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>8. Termination:</strong> We may terminate or suspend your
          access to the platform at any time, without prior notice or liability,
          for any reason, including if you breach these terms. Upon termination,
          your right to use the platform will immediately cease.
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>9. Disclaimers and Limitation of Liability:</strong>{' '}
          CarbonBright is provided "as is" and "as available" without warranties
          of any kind. We do not guarantee that the platform will be error-free
          or uninterrupted. To the maximum extent permitted by law, CarbonBright
          will not be liable for any indirect, incidental, special,
          consequential, or punitive damages arising out of or related to your
          use of the platform.
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>10. Governing Law:</strong> These terms shall be governed by
          and construed in accordance with the laws of Delaware, without regard
          to its conflict of law principles.
        </p>
        <p style={{ marginTop: 10 }}>
          <strong>11. Contact Information:</strong> If you have any questions
          about these Terms of Service, please contact us at
          <EMAIL>
        </p>

        <Divider style={{ marginTop: 20 }} />
        <strong>Acknowledgment</strong>
        <p>
          By using CarbonBright, you acknowledge that you have read, understood,
          and agree to be bound by these Terms of Service.
        </p>
      </Drawer>
    </>
  )
}

export default HelpAndSupport
