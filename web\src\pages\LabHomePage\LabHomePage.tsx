import { navigate, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'
import { Card, Col, Row, Layout, Badge, Tag, Button } from 'antd'
import type { TabsProps } from 'antd'
import { ArrowLeftOutlined } from '@ant-design/icons'
import Meta from 'antd/es/card/Meta'
import { useEffect, useState } from 'react'
import { useAuth } from 'src/auth'
const { Content } = Layout

const LabHomePage = () => {
  const { userMetadata } = useAuth()

  return (
    <>
      <Metadata title="Reports" description="Reports" />
      <Content>
        <Layout>
          <Content>
            <Card
              title="Welcome to Labs"
              bordered={false}
              style={{ marginTop: '15px', height: 700 }}
              extra={
                Object.keys(userMetadata?.orgIdToOrgMemberInfo ?? {}).length >
                  0 && (
                  <>
                    <Button
                      type="link"
                      icon={<ArrowLeftOutlined />}
                      onClick={(e) => {
                        navigate(routes.home())
                      }}
                    >
                      Back to Dashboard
                    </Button>
                  </>
                )
              }
            >
              <Card
                hoverable
                style={{ width: 350 }}
                cover={
                  <img
                    alt="Product Inventory Summary"
                    src="/images/labs_ef_matching.jpg"
                  />
                }
                onClick={(e) => {
                  navigate(routes.labEfMatching())
                }}
              >
                <Tag
                  style={{
                    float: 'right',
                    position: 'absolute',
                    zIndex: 1,
                    right: -25,
                    top: -10,
                  }}
                  color="#FFC000"
                >
                  NEW
                </Tag>
                <p
                  style={{
                    marginTop: '-10px',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    lineHeight: '1.25',
                  }}
                >
                  Emissions Factor Matching
                </p>
                <Meta
                  style={{ marginTop: '10px', fontSize: '14px' }}
                  description="Match your product raw materials emissions factors with our advanced AI Emission Factor Matching Tool"
                />
              </Card>
            </Card>
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default LabHomePage
