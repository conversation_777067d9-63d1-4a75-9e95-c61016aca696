import { test, expect, Request } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure');
  }
});

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
});

test.use({ storageState: CONST.authFile })

const requestTimings = new Map<Request, number>();

test('Clone Product', async ({ page }) => {
  test.setTimeout(420000)
  await page.setViewportSize({ width: 1920, height: 1080 });
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  page.on('request', (request) => {
    requestTimings.set(request, Date.now());
  });

  page.on('console', msg => {
    console.log(`[Console ${msg.type()}] ${msg.text()}`);
  });

  page.on('response', async (response) => {

    console.log('Response URL:', response.url());
    console.log('Response Status:', response.status());

    const request = response.request();
    const startTime = requestTimings.get(request);
    if (startTime) {
      const latency = Date.now() - startTime;
      console.log(`Response Time: ${latency}ms`);
      requestTimings.delete(request);
    }

    if (response.url().includes('/graphql')) {
      try {
        const responseBody = await response.json();
        console.log('GraphQL Response:', JSON.stringify(responseBody));
      } catch (error) {
        console.error('Error parsing response:', error)
      }
    }
  })

  await page
    .locator('input#search-input')
    .pressSequentially(CONST.testProduct.productName, { delay: 100 })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible({ timeout: 60000 })

  const selectProductCheckbox = await productInfo.locator(
    'td:nth-child(1) input'
  )

  await selectProductCheckbox.check()

  await page.click('button#action-dropdown-button')
  await page.click('button#clone-product-button')

  await page.context().storageState({ path: CONST.authFile })

  await page.screenshot({
    path: 'tests/screenshots/pre_clone.png',
  })

  await page.waitForSelector(
    `text=Product ${CONST.testProduct.productName} cloned successfully`,
    { timeout: 220000 }
  )

  await page.screenshot({
    path: 'tests/screenshots/post_clone.png',
  })

  await page.context().storageState({ path: CONST.authFile })
})

// test('Edit Cloned Product', async ({ page }) => {
//   test.setTimeout(120000)
//   await page.goto(`${CONST.baseURL}/products`)
//   await page.waitForSelector('text=Product Name')
//   await page.context().storageState({ path: CONST.authFile })

//   page.on('response', async (response) => {
//     if (response.url().includes('/graphql')) {
//       try {
//         const responseBody = await response.json();
//         console.log('GraphQL Response:', JSON.stringify(responseBody));
//       } catch (error) {
//         console.error('Error parsing response:', error)
//       }
//     }
//   })

//   await page.screenshot({
//     path: 'tests/screenshots/edit_clone_inventory_page.png',
//   })

//   await page
//     .locator('input#search-input')
//     .pressSequentially(`${CONST.testProduct.productName} (COPY)`, {
//       delay: 100,
//     })

//     await page.screenshot({
//       path: 'tests/screenshots/edit_clone_inventory_product_search.png',
//     })

//   const productInfo = page
//     .locator('table tr.ant-table-row.ant-table-row-level-0')
//     .first()

//   expect(productInfo).toBeVisible({ timeout: 1000 })

//   await page.screenshot({
//     path: 'tests/screenshots/edit_clone_inventory_product_search_filtered.png',
//   })

//   await productInfo.locator('td:nth-child(3)').click()

//   await page.screenshot({
//     path: 'tests/screenshots/edit_clone_product_info_locator_click.png',
//   })

//   await page.waitForSelector(`text=${CONST.testProduct.productName}`)

//   await page.screenshot({
//     path: 'tests/screenshots/edit_clone_product_info_page.png',
//   })

//   await page.getByRole('tab', { name: 'Lifecycle Inventory Data' }).click()

//   const rawMaterialInfo = page
//     .locator(
//       'div#rawMaterialsIngredients table tr.ant-table-row.ant-table-row-level-0'
//     )
//     .first()

//   await rawMaterialInfo.locator('td:nth-child(9) button').click()

//   await page.fill(
//     'input#basic_weight',
//     CONST.testProduct.ingredients[1].weightToBeUpdated ?? ''
//   )

//   await page.getByRole('button', { name: 'Save' }).click()

//   await page.waitForSelector(
//     `text=Ingredient weight for ${CONST.testProduct.ingredients[1].name} updated successfully`
//   )
// })


