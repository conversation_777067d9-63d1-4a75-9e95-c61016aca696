export const schema = gql`
  scalar JSON

  type ProcessModelEmissionsFactor {
    activityName: String
    referenceProduct: String
    geography: String
    source: String
    kgCO2e: Float
  }

  type ProductInfoProcessModelWalkerEmissionDetail {
    id: Int
    name: String
    amount: Float
    unit: String
    weight: Float
    distance: Float
    wttEmissions: Float
    emissionsFactor: ProcessModelEmissionsFactor
    totalEmissions: Float
    scrappageEmissions: Float
    recyclingEmissions: Float
    incinerationEmissions: Float
    landfillingEmissions: Float
    compostingEmissions: Float
  }

  type ProductInfoProcessModelWalkerEmission {
    totalEmissions: Float
    unit: String
    emissions: [ProductInfoProcessModelWalkerEmissionDetail]
  }

  type ProductInfoProcessModelWalker {
    materials: ProductInfoProcessModelWalkerEmission
    packaging: ProductInfoProcessModelWalkerEmission
    production: ProductInfoProcessModelWalkerEmission
    transportation: ProductInfoProcessModelWalkerEmission
    use: ProductInfoProcessModelWalkerEmission
    eol: ProductInfoProcessModelWalkerEmission
  }

  type ProductInfoProcessModelPcrWalker {
    sequenceNo: Int
    segmentName: String
    segmentEmissions: ProductInfoProcessModelWalkerEmission
  }

  type ImpactFactorDetail {
    lciaMethod: String
    category: String
    indicator: String
    emissionsFactor: ProcessModelEmissionsFactor
    totalEmissions: Float
  }

  type AllImpactFactorsEmissionDetail {
    id: Int
    name: String
    amount: Float
    unit: String
    impacts: JSON
  }

  type AllImpactFactorsEmission {
    unit: String
    emissions: [AllImpactFactorsEmissionDetail]
  }

  type AllImpactFactorsWalker {
    materials: AllImpactFactorsEmission
    packaging: AllImpactFactorsEmission
    production: AllImpactFactorsEmission
    transportation: AllImpactFactorsEmission
    use: AllImpactFactorsEmission
    eol: AllImpactFactorsEmission
  }

  type AllImpactFactorsPcrWalker {
    sequenceNo: Int
    segmentName: String
    segmentEmissions: AllImpactFactorsEmission
  }

  type ProductInfoProcessModelEdges {
    productId: String!
    fromNodeId: Int!
    toNodeId: Int!
  }

  type ProcessModelLocation {
    id: Int
    address1: String
    address2: String
    latitude: Float
    longitude: Float
    city: String
    stateOrProvince: String
    postalCode: String
    country: String
  }

  type ProcessModelEmissionsFactor {
    id: Int
    activityName: String
    geography: String
    source: String
    activityType: String
    referenceProduct: String
    referenceProductAmount: Float
    kgCO2e: Float
    unit: String
    activityDescription: String
  }

  type ProductInfoProcessModelNodeSupplier {
    id: Int
    supplierName: String
  }

  type ProductInfoProcessModelNode {
    id: Int
    productId: String
    name: String
    component: String
    description: String
    packagingLevel: String
    nodeType: String
    amount: Float
    scrapRate: Float
    scrapFate: String
    supplier: ProductInfoProcessModelNodeSupplier
    unit: String
    quantity: Int
    location: ProcessModelLocation
    emissionsFactor: ProcessModelEmissionsFactor
    massAllocationPerKg: Boolean
    recyclingDisposalRate: Float
    landfillDisposalRate: Float
    incinerationDisposalRate: Float
    compostingDisposalRate: Float
  }

  type ProductInfo {
    productId: String!
    productName: String!
    productType: String!
    brand: String
    manufacturer: String
    contentWeight: Float!
    weightUnit: String
    category: String
    productImage: String
    countryOfUse: String
    factoryCountry: String
    factoryCity: String
    annualSalesVolumeUnits: Int
    tags: [String]
    functionalUnit: String
    packageType: String
    clonedFromProductId: String
    nodes: [ProductInfoProcessModelNode]!
    edges: [ProductInfoProcessModelEdges]!
    emissions: ProductInfoProcessModelWalker
    pcrEmissions: [ProductInfoProcessModelPcrWalker]
    allImpactFactorsEmissions: AllImpactFactorsWalker
    pcrAllImpactFactorsEmissions: [AllImpactFactorsPcrWalker]
  }

  type Query {
    getProductInfo(
      productId: String!
      calculateEmissionsPerUnit: Boolean
    ): ProductInfo @requireAuth
  }
`
