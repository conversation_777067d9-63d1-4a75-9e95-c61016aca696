import React, { useState } from 'react'
import {
  Card,
  Progress,
  Typography,
  List,
  Modal,
  Space,
  Tooltip,
  Collapse,
  CollapseProps,
  theme,
} from 'antd'
import {
  QuestionCircleOutlined,
  RightOutlined,
  CloseOutlined,
} from '@ant-design/icons'
import { CategoryBar } from '@tremor/react'
import './custom.css'

const { Title, Text, Paragraph } = Typography

const ProductSustainabilityWidget = () => {
  const { token } = theme.useToken()

  const [visibleModal, setVisibleModal] = useState(null)

  const showModal = (modalName) => {
    setVisibleModal(modalName)
  }

  const hideModal = () => {
    setVisibleModal(null)
  }

  const renderClaimModal = (title, content) => (
    <Modal
      title={
        <Space>
          {content.icon}
          <span>{title}</span>
        </Space>
      }
      visible={visibleModal === title}
      onCancel={hideModal}
      footer={null}
      closeIcon={<CloseOutlined />}
    >
      <div className='modal-content-claims'>
      <div style={{ background: '#eeeeee', borderRadius: 10 }}>
        <div style={{ padding: '10px 10px 10px 10px' }}>
          {content.description && <Paragraph>{content.description}</Paragraph>}
          {content.achievements && (
            <>
              <Title level={5}>How we've achieved this:</Title>
              <ul>
                {content.achievements.map((achievement, index) => (
                  <li key={index}>&#8226; {achievement}</li>
                ))}
              </ul>
            </>
          )}
        </div>
      </div>
      <div style={{ background: '#eeeeee', borderRadius: 10, marginTop: 15 }}>
        <div style={{ padding: '10px 10px 10px 10px' }}>
          {content.verification && (
            <>
              <Title level={5}>Statement of Verification</Title>
              <Paragraph>{content.verification}</Paragraph>
              <Space>
                <a style={{ textDecoration: 'underline' }} href="#">
                  View evidence
                </a>
                <Text style={{ textDecoration: 'underline' }} type="secondary">
                  Valid until: {content.validUntil}
                </Text>
              </Space>
            </>
          )}
        </div>
      </div>
      <div style={{ background: '#eeeeee', borderRadius: 10, marginTop: 15 }}>
        <div style={{ padding: '10px 10px 10px 10px' }}>
          {content.verification && (
            <>
              <Title level={5}>About Product Sustainability Ratings</Title>
              <Paragraph>This product has been rated by CarbonBright’s Sustainability Impact Platform, Click <a href='#'>here</a> more information on the methodology as well as additional sustainability insights.</Paragraph>
              <Space>
                <p style={{ fontSize: 12 }}>
                  &copy; CarbonBright Inc. All Rights Reserved.
                </p>
                <img
                src="https://img1.wsimg.com/isteam/ip/d104b53e-d4dc-4010-b065-427183d3849b/CARBONBRIGHT-A3_SM.png"
                alt="CarbonBright"
                style={{
                  height: 20,
                  marginTop: -10,
                  marginLeft: 80,
                }}
              />
              </Space>
            </>
          )}
        </div>
      </div>
      </div>
    </Modal>
  )

  const panelStyle: React.CSSProperties = {
    marginBottom: 8,
    borderRadius: token.borderRadiusLG,
    border: `1px solid ${token.colorBorder}`,
  }

  const sustainabilityCredentialItems: CollapseProps['items'] = [
    {
      key: 'carbonNeutral',
      id: 'carbonNeutral',
      style: panelStyle,
      label: (
        <>
          <img
            src="/images/icons/co2.png"
            alt="Carbon Neutral"
            style={{ width: 24, height: 24, position: 'absolute' }}
          />
          <p style={{ fontSize: '16px', fontWeight: 'bold', marginLeft: 35 }}>
            Carbon Neutral
          </p>
        </>
      ),
      onClick: () => showModal('Carbon Neutral'),
      children: null,
    },
    {
      key: 'recycledPackaging',
      id: 'recycledPackaging',
      style: panelStyle,
      label: (
        <>
          <img
            src="/images/icons/recycle.png"
            alt="Carbon Neutral"
            style={{ width: 28, height: 24, position: 'absolute' }}
          />
          <p style={{ fontSize: '16px', fontWeight: 'bold', marginLeft: 35 }}>
            Recycled Packaging
          </p>
        </>
      ),
      onClick: () => showModal('Recycled Packaging'),
      children: null,
    },
    {
      key: 'reeduceWaterUse',
      id: 'reeduceWaterUse',
      style: panelStyle,
      label: (
        <>
          <img
            src="/images/icons/h2o.png"
            alt="Carbon Neutral"
            style={{ width: 20, height: 24, position: 'absolute' }}
          />
          <p style={{ fontSize: '16px', fontWeight: 'bold', marginLeft: 35 }}>
            Reduce Water Use
          </p>
        </>
      ),
      onClick: () => showModal('Reduce Water Use'),
      children: null,
    },
    {
      key: 'renewableEnergy',
      id: 'renewableEnergy',
      style: panelStyle,
      label: (
        <>
          <img
            src="/images/icons/renewable_energy.png"
            alt="Carbon Neutral"
            style={{ width: 24, height: 24, position: 'absolute' }}
          />
          <p style={{ fontSize: '16px', fontWeight: 'bold', marginLeft: 35 }}>
            Renewable Energy
          </p>
        </>
      ),
      onClick: () => showModal('Renewable Energy'),
      children: null,
    },
  ]

  const sustainabilityCredentials = [
    {
      title: 'Carbon Neutral',
      icon: (
        <img
          src="/images/icons/co2.png"
          alt="Carbon Neutral"
          style={{ width: 24, height: 24, marginRight: 8 }}
        />
      ),
      description:
        'This product is rated Carbon Neutral due to GHG Emissions reductions and/or Emissions Offsets used by the producer.',
      achievements: ['Carbon Offsets', 'Emissions Reductions'],
      verification:
        'The signatory company attests that it has purchased 6147 carbon units to offset emissions fully.',
      validUntil: '2025-04-04',
    },
    {
      title: 'Recycled Packaging',
      icon: (
        <img
          src="/images/icons/recycle.png"
          alt="Recycled Packaging"
          style={{ width: 24, height: 24, marginRight: 8 }}
        />
      ),
      description: "This product's packaging is made of recycled material.",
      achievements: [
        '50% of the packaging materials (calculated by weight) are from recycled origin',
      ],
      verification:
        'The signatory company attests that the aforementioned product packaging is is at least 50% recyclable.',
      validUntil: '2025-04-04',
    },
    {
      title: 'Reduce Water Use',
      icon: (
        <img
          src="/images/icons/h2o.png"
          alt="Reduce Water Use"
          style={{ width: 24, height: 24, marginRight: 8 }}
        />
      ),
      description:
        'This business has implemented one or more practices to reduce its water footprint at a business or product level.',
      achievements: [
        'Initiatives include water efficiency gains, reduced water consumption, and practices that address water insecurities.',
      ],
      verification:
        'The signatory company attests that it measured the total water footprint associated with this product and has taken active steps to reduce it.',
      validUntil: '2025-04-04',
    },
    {
      title: 'Renewable Energy',
      icon: (
        <img
          src="/images/icons/renewable_energy.png"
          alt="Renewable Energy"
          style={{ width: 24, height: 24, marginRight: 8 }}
        />
      ),
      description:
        'At least 80% of the energy used by the business is from renewable sources.',
      achievements: ['Energy sources from biomass, wind or solar power'],
      verification:
        'The signatory company attests that at least 80% of the energy used at the facility comes from renewable resources.',
      validUntil: '2025-04-04',
    },
  ]

  return (
    <>
      <Card style={{ width: 500 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
        <b style={{ fontSize: 16 }}>Sustainability</b>
          <div>
            <Space align="center">
              <Title level={5} style={{ margin: 0 }}>
                SUSTAINABILITY INDEX
              </Title>
              <Tooltip title="Explanation of sustainability index">
                <QuestionCircleOutlined />
              </Tooltip>
            </Space>
            <CategoryBar
              style={{ marginBottom: 20 }}
              showLabels={false}
              values={[20, 20, 20, 20, 20]}
              colors={['red', 'orange', 'yellow', 'lime', 'green']}
              markerValue={70}
              className="mt-3"
              showAnimation={true}
            />
            <Text style={{ marginTop: '10px !important' }}>
              This product is ranked{' '}
              <Text strong style={{ color: 'darkgreen' }}>
                better than average
              </Text>{' '}
              on it's sustainability impact compared to other products in the
              same category.
            </Text>
          </div>

          <Space
            align="baseline"
            style={{ justifyContent: 'space-between', width: '100%' }}
          >
            <Text strong>GHG EMISSIONS (kg CO2e/per use)</Text>
            <Title level={4} style={{ margin: 0 }}>
              1.75
            </Title>
          </Space>
        </Space>
      </Card>

      <Card style={{ width: 500, marginTop: 10, backgroundColor: 'transparent'}}>
        <Space direction="vertical" style={{ width: '100%' }}>
            <b style={{ fontSize: 16 }}>Sustainability Credentials</b>
          <Collapse
            style={{
              backgroundColor: 'transparent',
              background: token.colorBgContainer,
              marginTop: 10,
              cursor: 'pointer',
            }}
            defaultActiveKey={['productDetails']}
            items={sustainabilityCredentialItems}
            expandIconPosition="right"
            bordered={false}
            collapsible={'icon'}
          />
          <div style={{ float: 'right' }}>
            <p style={{ fontSize: '12px', marginRight: 120 }}>
              Powered By
              <img
                src="https://img1.wsimg.com/isteam/ip/d104b53e-d4dc-4010-b065-427183d3849b/CARBONBRIGHT-A3_SM.png"
                alt="CarbonBright"
                style={{
                  height: 20,
                  position: 'absolute',
                  marginTop: -20,
                  marginLeft: 80,
                }}
              />
            </p>
          </div>
        </Space>

        {sustainabilityCredentials.map((credential) =>
          renderClaimModal(credential.title, credential)
        )}
      </Card>
    </>
  )
}

export default ProductSustainabilityWidget
