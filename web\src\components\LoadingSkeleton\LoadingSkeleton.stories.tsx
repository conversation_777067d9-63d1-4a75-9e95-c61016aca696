// Pass props to your component by passing an `args` object to your story
//
// ```jsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import LoadingSkeleton from './LoadingSkeleton'

const meta: Meta<typeof LoadingSkeleton> = {
  component: LoadingSkeleton,
}

export default meta

type Story = StoryObj<typeof LoadingSkeleton>

export const Primary: Story = {}
