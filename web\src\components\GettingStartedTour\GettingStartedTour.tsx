import { Tour, Checkbox, Button } from 'antd'
import type { TourProps } from 'antd'
import { navigate, routes } from '@redwoodjs/router'
import { useMutation } from '@redwoodjs/web'

const LOG_PRODUCT_TOUR_STATUS = gql`
  mutation LogProductTourStatus($tourCompleted: Boolean!) {
    logProductTourStatus(tourCompleted: $tourCompleted)
  }
`

const GettingStartedTour = ({ isOpen, onClose }) => {
  const [logProductTourStatus] = useMutation(LOG_PRODUCT_TOUR_STATUS)

  const handleIgnoreTourCheckboxChange = async (event) => {
    await logProductTourStatus({
      variables: { tourCompleted: event.target.checked },
    })
  }

  const guidedTourSteps: TourProps['steps'] = [
    {
      title: <p style={{ fontSize: '18px' }}>Welcome to CarbonBright!</p>,
      description: (
        <>
          <img style={{ width: '100%' }} src="/images/tour/tour-1.png" />
          <p style={{ fontSize: '16px', marginTop: '15px' }}>
            We'd love to show you around - it'll be fast, promise. You'll see
            where useful features are, so you get off to a flying start.
          </p>
          <br />
          <Checkbox onChange={handleIgnoreTourCheckboxChange}>
            Don't show this again
          </Checkbox>
          <Button
            style={{
              fontWeight: 600,
              width: '100px',
              height: '30px',
              color: '#5656fa',
              position: 'absolute',
              marginTop: '30px',
              right: '130px',
            }}
            onClick={onClose}
          >
            Exit
          </Button>
        </>
      ),
      nextButtonProps: {
        className: 'animate-pulse',
        children: 'Next',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: '#f3c314d4',
        },
      },
      prevButtonProps: {
        children: 'Exit',
        style: { display: 'inline-block' },
      },
      onPrev: onClose,
    },
    {
      title: <p style={{ fontSize: '18px' }}>Step 1 - Create Your Product</p>,
      description: (
        <>
          <img style={{ width: '100%' }} src="/images/tour/tour-2.png" />
          <p style={{ fontSize: '16px', marginTop: '15px' }}>
            Start by entering some basic details of your product. Our AI-based
            LCA Co-pilot will guide you through the rest of the process,
            ensuring you capture all necessary information, and automating more
            complex tasks.
          </p>
        </>
      ),
      nextButtonProps: {
        className: 'animate-pulse',
        children: 'Next',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: '#f3c314d4',
        },
      },
      prevButtonProps: {
        children: 'Back',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: 'white',
        },
      },
    },
    {
      title: <p style={{ fontSize: '18px' }}>Step 2 - View Insights</p>,
      description: (
        <>
          <img style={{ width: '100%' }} src="/images/tour/tour-3.png" />
          <p style={{ fontSize: '16px', marginTop: '15px' }}>
            Discover key insights about your product's environmental impact. Our
            AI-driven platform provides detailed analysis and visualizations to
            help you understand the emissions hotspots and opportunities for
            improvement.
          </p>
        </>
      ),
      nextButtonProps: {
        className: 'animate-pulse',
        children: 'Next',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: '#f3c314d4',
        },
      },
      prevButtonProps: {
        children: 'Back',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: 'white',
        },
      },
    },
    {
      title: (
        <p style={{ fontSize: '18px' }}>Step 3 - Design Better Products</p>
      ),
      description: (
        <>
          <img style={{ width: '100%' }} src="/images/tour/tour-4.png" />
          <p style={{ fontSize: '16px', marginTop: '15px' }}>
            Use the insights gained to redesign your product with sustainability
            in mind. Compare choices and alternative materials to help you
            reduce your product's environmental footprint.
          </p>
        </>
      ),
      nextButtonProps: {
        className: 'animate-pulse',
        children: 'Next',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: '#f3c314d4',
        },
      },
      prevButtonProps: {
        children: 'Back',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: 'white',
        },
      },
    },
    {
      title: <p style={{ fontSize: '18px' }}>Step 4 - Share Your Results</p>,
      description: (
        <>
          <img style={{ width: '100%' }} src="/images/tour/tour-5.png" />
          <p style={{ fontSize: '16px', marginTop: '15px' }}>
            Communicate your sustainability efforts by sharing detailed reports
            and visuals with stakeholders. Our tool makes it easy to export and
            present your findings, demonstrating your commitment to transparency
            & a more sustainable future.
          </p>
        </>
      ),
      nextButtonProps: {
        className: 'animate-pulse',
        children: 'Next',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: '#f3c314d4',
        },
      },
      prevButtonProps: {
        children: 'Back',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: 'white',
        },
      },
    },
  ]

  return (
    <Tour
      type="default"
      open={isOpen}
      onClose={onClose}
      steps={guidedTourSteps}
      onFinish={() => navigate(routes.products())}
    />
  )
}

export default GettingStartedTour
