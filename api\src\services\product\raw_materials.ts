import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper';
import { GraphQLError } from 'graphql';


export const getRawMaterials = async ({ isPackaging }) => {
  try {

    const params = isPackaging ? { is_packaging: sanitizeInput(isPackaging) } : {};

    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/raw-materials`,
      { params }
    )

    const rawMaterials = response.data

    if (!Array.isArray(rawMaterials)) {
      throw new Error('Unexpected response format from the server.')
    }

    return rawMaterials.map((rawMaterial) => {
      return {
        name: rawMaterial.name,
        description: rawMaterial.description,
        casNumber: rawMaterial.cas_number,
      }
    })
  } catch (error) {
    let errorMessage = 'Error fetching raw materials. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
