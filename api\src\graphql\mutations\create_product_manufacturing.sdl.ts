export const schema = gql`

  input ManufacturingProcessEmissionsFactor {
    activity_name: String
    geography: String
    reference_product: String
    kg_co2e: Float
    source: String
  }

  input CreateProductManufacturingProcessInput {
    process_name: String!
    geography_iso3: String!
    electricity_kwh: Float
    water_liters: Float
    emissions_factor: ManufacturingProcessEmissionsFactor
  }

  type Mutation {
    createProductManufacturing(
      productId: String!
      manufacturingProcess: [CreateProductManufacturingProcessInput!]!
    ): String @requireAuth
  }
`
