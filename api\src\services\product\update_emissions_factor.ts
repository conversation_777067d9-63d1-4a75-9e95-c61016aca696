import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const updateEmissionsFactor = async ({
  emissionsFactorId,
  emissionsFactor,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.put(
      `${
        process.env.LCA_API_ENDPOINT
      }/emissions-factors/${tenantID}/emissions-factor/${sanitizeInput(emissionsFactorId)}`,
      sanitizeInput(emissionsFactor)
    )

    return {
      id: response.data.id,
      activityName: response.data.activity_name,
      referenceProduct: response.data.reference_product,
      description: response.data.activity_description,
    }
  } catch (error) {
    let errorMessage = 'Error updating emissions factor. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
