import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import axiosRetry from 'axios-retry'
import { GraphQLError } from 'graphql'

axiosRetry(axios, {
  retries: 3,
  retryDelay: axiosRetry.exponentialDelay,
});

export const predictProductCategory = async ({ productName }) => {
  try {
    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/product-categories/predict-category`,
      {
        params: {
          product_name: sanitizeInput(productName),
        },
      }
    )

    const productCategory = response.data

    return productCategory
  } catch (error) {
    let errorMessage = 'Error predicting product category. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
