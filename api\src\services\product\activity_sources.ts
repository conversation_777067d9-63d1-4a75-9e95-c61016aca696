import axios from 'axios'
import { GraphQLError } from 'graphql'
import { context } from '@redwoodjs/graphql-server'

export const getActivitySources = async () => {
  try {

    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/emissions-factors/${tenantID}/sources`
    )

    return response.data.map((source) => ({
      source: source
    }))

  } catch (error) {
    let errorMessage = 'Error fetching activity sources. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}