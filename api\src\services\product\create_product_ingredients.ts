import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const createProductIngredients = async ({
  productId,
  productIngredients,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.post(
      `${process.env.LCA_API_ENDPOINT}/products/${tenantID}/${sanitizeInput(productId)}/product-ingredients`,
      {
        ingredients: sanitizeInput(productIngredients),
      }
    )

    response.data.map((ingredient) => {
      return {
        ingredientName: ingredient.ingredient_name,
        weight: ingredient.weight,
      }
    })
  } catch (error) {
    let errorMessage = 'Error creating product ingredients. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
