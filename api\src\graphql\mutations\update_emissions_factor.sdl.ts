export const schema = gql`
  type UpdatedEmissionsFactor {
    id: Int!
    activityName: String!
    referenceProduct: String!
    description: String
  }

  input UpdateEmissionsFactorInput {
    activity_name: String!
    reference_product_name: String!
    activity_description: String
  }

  type Mutation {
    updateEmissionsFactor(
      emissionsFactorId: Int!
      emissionsFactor: UpdateEmissionsFactorInput!
    ): UpdatedEmissionsFactor! @requireAuth
  }
`
