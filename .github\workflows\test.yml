name: Test Against Staging LCA

on:
  pull_request

env:
  PYTHON_VERSION: '3.10'
  TESTING_MODE: 'True'
  BASE_URL: 'http://localhost:8910'
  REGISTRY: ghcr.io
  LCA_IMAGE_NAME: carbonbright/product_lca
  TAG: staging
  AZURE_WEBAPP_NAME: carbonbright-lca
  LCA_PORT: 5000
  MYSQL_TAG: "8.0"
  MYSQL_CONTAINER_NAME: lca-mysql
  HF_TOKEN: ${{ secrets.TESTING_HF_TOKEN }}
  MAPBOX_ACCESS_TOKEN: ${{ secrets.TESTING_MAPBOX_ACCESS_TOKEN }}
  DB_CONNECTION_STRING: 'mysql+pymysql://root@127.0.0.1'
  MYSQL_SERVICE_NAME: 'lca-mysql'
  ORS_TOKEN: ${{ secrets.TESTING_ORS_TOKEN }}
  GOOGLE_MAPS_API_KEY: ${{ secrets.TESTING_GOOGLE_MAPS_API_KEY }}
  PROPEL_AUTH_TOKEN: ${{ secrets.TESTING_PROPELAUTH_API_TOKEN }}
  PROPEL_AUTH_URL: 'https://6044936.propelauthtest.com'
  PROPEL_AUTH_LOGIN_URL: 'https://6044936.propelauthtest.com/en/login'
  PROPELAUTH_VERIFIER_KEY: ${{ secrets.TESTING_PROPELAUTH_VERIFIER_KEY }}
  ML_MODELS_ENDPOINT: ${{ secrets.ML_MODELS_ENDPOINT }}
  SENTRY_DSN: ${{ secrets.TESTING_SENTRY_DSN }}


jobs:
  setup-and-test:
    runs-on: runner-16GB-u2204

    permissions:
      contents: read

    steps:
    - name: Log in to the Container registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ vars.DOCKER_REGISTRY_SERVER_USERNAME }}
        password: ${{ secrets.DOCKER_REGISTRY_SERVER_PASSWORD }}

    - name: Pull LCA Docker Image
      run: docker pull ${{ env.REGISTRY }}/${{ env.LCA_IMAGE_NAME }}:${{ env.TAG }}

    - name: Setup Mysql Docker
      run: |
        if [ "$(docker ps -aq -f name=^${{ env.MYSQL_CONTAINER_NAME }})" ]; then
            echo "Container ${{ env.MYSQL_CONTAINER_NAME }} exists. Removing it..."
            docker stop ${{ env.MYSQL_CONTAINER_NAME }}
            docker rm -f ${{ env.MYSQL_CONTAINER_NAME }}
        fi
        echo -e "[mysqld]\nlocal_infile=1" > /tmp/my.cnf
        echo "Starting mysql container..."
        docker run --name ${{ env.MYSQL_CONTAINER_NAME }} --network=host -e MYSQL_ALLOW_EMPTY_PASSWORD=yes -p 3306:3306 -v /tmp/my.cnf:/etc/my.cnf -d mysql:${{ env.MYSQL_TAG }}

        until docker exec ${{ env.MYSQL_CONTAINER_NAME }} mysqladmin ping --silent; do
            echo "Waiting for mysql to start..."
            sleep 2
        done

    - name: Run LCA Docker Image
      run: |
        docker run \
        --name ${{ env.AZURE_WEBAPP_NAME }}-setup \
        --network=host -p ${{ env.LCA_PORT }}:${{ env.LCA_PORT }} \
        -e HF_TOKEN=${{ env.HF_TOKEN }} \
        -e MAPBOX_ACCESS_TOKEN=${{ env.MAPBOX_ACCESS_TOKEN }} \
        -e DB_CONNECTION_STRING=${{ env.DB_CONNECTION_STRING }} \
        -e ORS_TOKEN=${{ env.ORS_TOKEN }} \
        -e GOOGLE_MAPS_API_KEY=${{ env.GOOGLE_MAPS_API_KEY }} \
        -e PROPEL_AUTH_TOKEN=${{ env.PROPEL_AUTH_TOKEN }} \
        -e PROPEL_AUTH_URL=${{ env.PROPEL_AUTH_URL }} \
        -e TESTING_MODE=${{ env.TESTING_MODE }} \
        -e BACKGROUND_SEEDING=false \
        -e ML_MODELS_ENDPOINT=${{ env.ML_MODELS_ENDPOINT }} \
        --entrypoint /bin/sh \
        ${{ env.REGISTRY }}/${{ env.LCA_IMAGE_NAME }}:${{ env.TAG }} \
        -c "python cli.py --setup-server --tenant-ids=demo1,TestTrial"

        docker run -d \
        --name ${{ env.AZURE_WEBAPP_NAME }} \
        --network=host -p ${{ env.LCA_PORT }}:${{ env.LCA_PORT }} \
        -e HF_TOKEN=${{ env.HF_TOKEN }} \
        -e MAPBOX_ACCESS_TOKEN=${{ env.MAPBOX_ACCESS_TOKEN }} \
        -e DB_CONNECTION_STRING=${{ env.DB_CONNECTION_STRING }} \
        -e ORS_TOKEN=${{ env.ORS_TOKEN }} \
        -e GOOGLE_MAPS_API_KEY=${{ env.GOOGLE_MAPS_API_KEY }} \
        -e PROPEL_AUTH_TOKEN=${{ env.PROPEL_AUTH_TOKEN }} \
        -e PROPEL_AUTH_URL=${{ env.PROPEL_AUTH_URL }} \
        -e TESTING_MODE=${{ env.TESTING_MODE }} \
        -e BACKGROUND_SEEDING=false \
        -e ML_MODELS_ENDPOINT=${{ env.ML_MODELS_ENDPOINT }} \
        ${{ env.REGISTRY }}/${{ env.LCA_IMAGE_NAME }}:${{ env.TAG }}

    - name: Checkout Web App
      uses: actions/checkout@v4

    - name: Setup Node
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'

    - name: Install Web App Dependencies
      run: yarn install

    - name: Run E2E Web App Tests
      id: e2e_tests
      env:
        LCA_API_ENDPOINT: 'http://127.0.0.1:${{ env.LCA_PORT }}'
        PROPELAUTH_API_TOKEN: ${{ env.PROPEL_AUTH_TOKEN }}
        PROPELAUTH_VERIFIER_KEY: ${{ env.PROPELAUTH_VERIFIER_KEY }}
        PROPELAUTH_AUTH_URL: ${{ env.PROPEL_AUTH_URL }}
        MAPBOX_SECRET_TOKEN: ${{ secrets.TESTING_MAPBOX_SECRET_TOKEN }}
        TEST_USER_EMAIL:  ${{ secrets.TESTING_USER_EMAIL }}
        TEST_USER_PASSWORD: ${{ secrets.TESTING_USER_PASSWORD }}
        TEST_TRIAL_USER_EMAIL: ${{ secrets.TESTING_TRIAL_USER_EMAIL }}
        TEST_TRIAL_USER_PASSWORD: ${{ secrets.TESTING_TRIAL_USER_PASSWORD }}
        PROPELAUTH_LOGIN_URL: ${{ env.PROPEL_AUTH_LOGIN_URL }}
        SERVICE_HUB_PORTAL_ID: ${{ secrets.TESTING_SERVICE_HUB_PORTAL_ID }}
        TEST_PROCESS_MODEL_USER_EMAIL:  ${{ secrets.TESTING_TRIAL_USER_EMAIL }}
        TEST_PROCESS_MODEL_USER_PASSWORD: ${{ secrets.TESTING_TRIAL_USER_PASSWORD }}
      run: |
        mkdir -p tests/screenshots
        yarn test:e2e

    - name: Upload E2E Web App Tests report
      if: ${{ failure() && steps.e2e_tests.outcome == 'failure' }}
      uses: actions/upload-artifact@v4
      with:
        name: test-report-e2e
        path: |
          playwright-report/
          test-results/
          app.log

    - name: Run Processmodel Tests
      if: ${{ success() }}
      id: processmodel_tests
      env:
        LCA_API_ENDPOINT: 'http://127.0.0.1:${{ env.LCA_PORT }}'
        PROPELAUTH_API_TOKEN: ${{ env.PROPEL_AUTH_TOKEN }}
        PROPELAUTH_VERIFIER_KEY: ${{ env.PROPELAUTH_VERIFIER_KEY }}
        PROPELAUTH_AUTH_URL: ${{ env.PROPEL_AUTH_URL }}
        MAPBOX_SECRET_TOKEN: ${{ secrets.TESTING_MAPBOX_SECRET_TOKEN }}
        TEST_USER_EMAIL:  ${{ secrets.TESTING_USER_EMAIL }}
        TEST_USER_PASSWORD: ${{ secrets.TESTING_USER_PASSWORD }}
        TEST_TRIAL_USER_EMAIL: ${{ secrets.TESTING_TRIAL_USER_EMAIL }}
        TEST_TRIAL_USER_PASSWORD: ${{ secrets.TESTING_TRIAL_USER_PASSWORD }}
        PROPELAUTH_LOGIN_URL: ${{ env.PROPEL_AUTH_LOGIN_URL }}
        SERVICE_HUB_PORTAL_ID: ${{ secrets.TESTING_SERVICE_HUB_PORTAL_ID }}
        TEST_PROCESS_MODEL_USER_EMAIL:  ${{ secrets.TESTING_TRIAL_USER_EMAIL }}
        TEST_PROCESS_MODEL_USER_PASSWORD: ${{ secrets.TESTING_TRIAL_USER_PASSWORD }}
      run: |
        mkdir -p tests/screenshots
        yarn test:processModel

    - name: Upload Processmodel Tests report
      if: ${{ failure() && steps.processmodel_tests.outcome == 'failure' }}
      uses: actions/upload-artifact@v4
      with:
        name: test-report-processmodel
        path: |
          playwright-report/
          test-results/
          app.log

    - name: Run Labs Web App Tests
      id: labs_tests
      if: ${{ success() }}
      env:
        LCA_API_ENDPOINT: 'http://127.0.0.1:${{ env.LCA_PORT }}'
        PROPELAUTH_API_TOKEN: ${{ env.PROPEL_AUTH_TOKEN }}
        PROPELAUTH_VERIFIER_KEY: ${{ env.PROPELAUTH_VERIFIER_KEY }}
        PROPELAUTH_AUTH_URL: ${{ env.PROPEL_AUTH_URL }}
        MAPBOX_SECRET_TOKEN: ${{ secrets.TESTING_MAPBOX_SECRET_TOKEN }}
        TEST_USER_EMAIL:  ${{ secrets.TESTING_USER_EMAIL }}
        TEST_USER_PASSWORD: ${{ secrets.TESTING_USER_PASSWORD }}
        TEST_TRIAL_USER_EMAIL: ${{ secrets.TESTING_TRIAL_USER_EMAIL }}
        TEST_TRIAL_USER_PASSWORD: ${{ secrets.TESTING_TRIAL_USER_PASSWORD }}
        TEST_LAB_USER_EMAIL: ${{ secrets.TESTING_LAB_USER_EMAIL }}
        TEST_LAB_USER_PASSWORD: ${{ secrets.TESTING_LAB_USER_PASSWORD }}

        PROPELAUTH_LOGIN_URL: ${{ env.PROPEL_AUTH_LOGIN_URL }}
        SERVICE_HUB_PORTAL_ID: ${{ secrets.TESTING_SERVICE_HUB_PORTAL_ID }}
      run: |
        mkdir -p tests/screenshots
        yarn test:labs

    - name: Upload Labs Web App Tests report
      if: ${{ failure() && steps.labs_tests.outcome == 'failure' }}
      uses: actions/upload-artifact@v4
      with:
        name: test-report-labs
        path: |
          playwright-report/
          test-results/
          app.log

    - name: Extract Docker stdout logs
      if: ${{ failure() }}
      run: |
        mkdir -p docker_logs
        docker logs ${{ env.AZURE_WEBAPP_NAME }} > docker_logs/docker_stdout.log

    - name: Extract internal log files
      if: ${{ failure() }}
      run: |
        CURRENT_DATE=$(date +"%Y%m%d")
        mkdir -p internal_logs
        docker cp ${{ env.AZURE_WEBAPP_NAME }}:/app/logs/lca_calculator_core_$CURRENT_DATE.log internal_logs/ || echo "Log file not found"

    - name: Upload Docker stdout logs
      if: ${{ failure() }}
      uses: actions/upload-artifact@v4
      with:
        name: docker-stdout-logs
        path: docker_logs/

    - name: Upload internal log files
      if: ${{ failure() }}
      uses: actions/upload-artifact@v4
      with:
        name: internal-lca-logs
        path: internal_logs/

    - name: Print LCA Docker logs
      if: always()
      run: docker logs ${{ env.AZURE_WEBAPP_NAME }}

