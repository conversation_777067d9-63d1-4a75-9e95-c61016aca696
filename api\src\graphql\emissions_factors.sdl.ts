export const schema = gql`
  type ActivityDatasetEmissionsFactors {
    efId: Int!
    activityName: String!
    description: String
    referenceProduct: String
    geography: String
    source: String
    unit: String
    isTenant: Boolean
  }

  type Query {
    getEmissionsFactors(
      activityName: String
      geography: [String]
      source: [String]
      unit: [String]
    ): [ActivityDatasetEmissionsFactors] @requireAuth
  }
`
