import type { SupplierInfoQuery } from 'types/graphql'
import type { CellSuccessProps, CellFailureProps } from '@redwoodjs/web'
import { useLazyQuery } from '@apollo/client'
import { Link, routes } from '@redwoodjs/router'
import {
  Layout,
  Breadcrumb,
  Row,
  Col,
  Tag,
  Flex,
  Divider,
  Tabs,
  Card,
  Descriptions,
} from 'antd'
const { Content } = Layout
import { CheckCircleOutlined } from '@ant-design/icons'
import { useEffect, useState } from 'react'
const { TabPane } = Tabs
import DataTable from '../DataTable/DataTable'
import mapboxgl from 'mapbox-gl'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import 'mapbox-gl/dist/mapbox-gl.css'
import {
  getMapboxAccessTokenFromCache,
  setMapboxAccessTokenToCache,
} from 'src/utils/mapbox'
import './custom.css'
import ErrorHandler from '../ErrorHandler/ErrorHandler'
import { renderImpactFactorUnit } from 'src/utils/helper'

export const QUERY = gql`
  query SupplierInfoQuery($supplierId: String!) {
    getSupplierInfo(supplierId: $supplierId) {
      supplierName
      supplierId
      supplierLevel
      supplierType
      country
      city
      latitude
      longitude
      primaryAddress1
      primaryAddress2
      taxRegistryNumber
      primaryContactName
      website
      stateOrProvince
      postalCode
      totalProducts
      products {
        productName
        productId
        category
        brand
        totalEmissions
        scope3emissions
        rawMaterialEmissions {
          ingredientName
          totalEmissions
          ingredientEmissions
        }
        packagingEmissions {
          packageMaterial
          totalEmissions
        }
        ingredientDistributionEmissions {
          ingredientName
          totalEmissions
          transportEmissions
        }
        packagingDistributionEmissions {
          packageMaterial
          totalEmissions
          transportEmissions
        }
        annualSalesVolumeUnits
      }
      scope3emissions
    }
  }
`

const MAPBOX_ACCESS_TOKEN_QUERY = gql`
  query MapboxAccessTokenQuery {
    getMapboxAccessToken {
      accessToken
      expiresAt
    }
  }
`

export const Loading = () => <div>Loading...</div>

export const Empty = () => <div>Empty</div>

export const Failure = ({ error }: CellFailureProps) => (
  <ErrorHandler error={error} />
)

export const Success = (props: CellSuccessProps<SupplierInfoQuery>) => {
  if (!props) {
    return <ErrorHandler />
  }

  const supplierInfo = props.getSupplierInfo

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const tenantID =
    orgMemberInfo && orgMemberInfo.urlSafeOrgName
      ? orgMemberInfo.urlSafeOrgName
      : null

  const [
    fetchMapboxAccessToken,
    { data: mapboxAccessTokenData, error: mapboxAccessTokenDataError },
  ] = useLazyQuery(MAPBOX_ACCESS_TOKEN_QUERY)

  const [mapBoxAccessToken, setMapBoxAccessToken] = useState(
    getMapboxAccessTokenFromCache()
  )
  const [loadMap, setLoadMap] = useState(false)

  useEffect(() => {
    if (mapBoxAccessToken) {
      mapboxgl.accessToken = mapBoxAccessToken.accessToken
      setMapboxAccessTokenToCache(mapBoxAccessToken)
      setLoadMap(true)
    }
    if (!mapBoxAccessToken) {
      fetchMapboxAccessToken()
    }
  }, [mapBoxAccessToken, fetchMapboxAccessToken])

  useEffect(() => {
    if (mapboxAccessTokenData) {
      setMapBoxAccessToken(mapboxAccessTokenData.getMapboxAccessToken)
    }
  }, [mapboxAccessTokenData])

  useEffect(() => {
    if (loadMap) {
      const mapboxMap = new mapboxgl.Map({
        container: 'mapboxMap',
        style: 'mapbox://styles/mapbox/streets-v12',
        center: [supplierInfo.longitude, supplierInfo.latitude],
        zoom: 10,
      })

      const locationMarker = new mapboxgl.Marker()
        .setLngLat([supplierInfo.longitude, supplierInfo.latitude])
        .addTo(mapboxMap)

      mapboxMap.on('load', () => {
        setLoadMap(false)
      })
    }
  }, [loadMap, supplierInfo.latitude, supplierInfo.longitude])

  const labelStyle = {
    fontWeight: 'bold',
    color: 'black',
  }

  const supplierProductInventoryTableColumns = [
    {
      title: 'SKU',
      dataIndex: 'productId',
      sorter: true,
    },
    {
      title: 'Name',
      dataIndex: 'productName',
      render: (text, record) => (
        <Link
          to={routes.productDetail({
            tenantID: tenantID,
            productId: encodeURIComponent(record.productId),
          })}
        >
          <b>{text}</b>
        </Link>
      ),
      sorter: true,
    },
    {
      title: 'Brand',
      dataIndex: 'brand',
      sorter: true,
    },
    {
      title: 'Units',
      dataIndex: 'annualSalesVolumeUnits',
      render: (text) => <p>{text.toLocaleString()}</p>,
      sorter: true,
    },
    {
      title: `Product Footprint (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'totalEmissions',
      render: (text, record) => (
        <Tag color="blue">
          {parseFloat(text).toLocaleString(undefined, {
            minimumFractionDigits: 4,
          })}
        </Tag>
      ),
      sorter: true,
    },
    {
      title: `Scope 3 Emissions (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'scope3emissions',
      render: (text, record) => (
        <Tag color="blue">
          {parseFloat(text).toLocaleString(undefined, {
            minimumFractionDigits: 4,
          })}
        </Tag>
      ),
      sorter: true,
    },
  ]

  return (
    <>
      <Row>
        <Col flex="auto">
          <Breadcrumb
            items={[
              {
                title: <a href={routes.suppliers()}>Suppliers</a>,
              },
              {
                title: supplierInfo.supplierName,
              },
            ]}
          ></Breadcrumb>
        </Col>
        <Col flex="0">
          <Flex gap="small">
            <span>Status</span>
            <Tag icon={<CheckCircleOutlined />} color="success">
              Active
            </Tag>
          </Flex>
        </Col>
      </Row>
      <Divider orientation="left"></Divider>
      <Layout>
        <Content style={{ backgroundColor: 'transparent', display: 'flex' }}>
          <Card
            title={
              <p
                style={{
                  whiteSpace: 'normal',
                  wordWrap: 'break-word',
                  overflowWrap: 'break-word',
                }}
              >
                {supplierInfo.supplierName}
              </p>
            }
            style={{
              maxWidth: '300px',
              border: 0,
              flex: '0 0 300px',
              backgroundColor: 'transparent',
            }}
          >
            <div
              id="mapboxMap"
              style={{
                marginLeft: '-30px',
                height: '250px',
                border: '2px solid lightgrey',
                borderRadius: '8px',
                marginTop: -24,
              }}
            />
            <Descriptions
              style={{ marginLeft: '-50px', marginTop: '10px' }}
              column={1}
              bordered
            >
              <Descriptions.Item
                style={{ background: 'transparent', border: 'none' }}
                label={<span style={labelStyle}>Supplier ID</span>}
              >
                {supplierInfo.supplierName}
              </Descriptions.Item>
              <Descriptions.Item
                style={{ background: 'transparent', border: 'none' }}
                label={<span style={labelStyle}>Type</span>}
              >
                {supplierInfo.supplierType}
              </Descriptions.Item>
              <Descriptions.Item
                style={{ background: 'transparent', border: 'none' }}
                label={<span style={labelStyle}>Level</span>}
              >
                <TabPane tab="Report" key="4"></TabPane>
                Tier {supplierInfo.supplierLevel}
              </Descriptions.Item>
              <Descriptions.Item
                style={{ background: 'transparent', border: 'none' }}
                label={<span style={labelStyle}>Location</span>}
              >
                {supplierInfo.city}, {supplierInfo.country}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* Tabs */}
          <div
            style={{
              flex: '2',
              borderRadius: '5px',
              backgroundColor: 'white',
            }}
          >
            <Tabs defaultActiveKey="1" style={{ margin: '10px' }}>
              <TabPane tab="Company Details" key="2">
                <Descriptions
                  title="Company Details"
                  layout="horizontal"
                  column={1}
                  style={{ width: '800px', marginTop: '10px' }}
                  bordered
                >
                  <Descriptions.Item
                    style={{ width: '50%' }}
                    label="Company Name"
                  >
                    {supplierInfo.supplierName}
                  </Descriptions.Item>
                  <Descriptions.Item
                    style={{ width: '50%' }}
                    label="Tax/Registration #"
                  >
                    {supplierInfo.taxRegistryNumber || 'N/A'}
                  </Descriptions.Item>
                </Descriptions>
                <Descriptions
                  title="Primary Contact"
                  layout="horizontal"
                  column={1}
                  style={{ width: '800px', marginTop: '10px' }}
                  bordered
                >
                  <Descriptions.Item
                    style={{ width: '50%' }}
                    label="Contact Name"
                  >
                    {supplierInfo.primaryContactName || 'N/A'}
                  </Descriptions.Item>
                  <Descriptions.Item
                    style={{ width: '50%' }}
                    label="Contact Email"
                  >
                    {supplierInfo.primaryContactEmail || 'N/A'}
                  </Descriptions.Item>
                </Descriptions>
                <Descriptions
                  title="Primary Address"
                  layout="horizontal"
                  column={1}
                  style={{ width: '800px', marginTop: '10px' }}
                  bordered
                >
                  <Descriptions.Item style={{ width: '50%' }} label="Address 1">
                    {supplierInfo.primaryAddress1 || 'N/A'}
                  </Descriptions.Item>
                  <Descriptions.Item style={{ width: '50%' }} label="Address 2">
                    {supplierInfo.primaryAddress2 || 'N/A'}
                  </Descriptions.Item>
                  <Descriptions.Item style={{ width: '50%' }} label="City">
                    {supplierInfo.city}
                  </Descriptions.Item>
                  <Descriptions.Item
                    style={{ width: '50%' }}
                    label="State/Province"
                  >
                    {supplierInfo.stateOrProvince || 'N/A'}
                  </Descriptions.Item>
                  <Descriptions.Item
                    style={{ width: '50%' }}
                    label="Postal Code"
                  >
                    {supplierInfo.postalCode || 'N/A'}
                  </Descriptions.Item>
                  <Descriptions.Item style={{ width: '50%' }} label="Country">
                    {supplierInfo.country || 'N/A'}
                  </Descriptions.Item>
                  <Descriptions.Item style={{ width: '50%' }} label="Website">
                    {supplierInfo.website || 'N/A'}
                  </Descriptions.Item>
                </Descriptions>
              </TabPane>
              <TabPane tab="Products" key="3">
                <h2>Purchased Products</h2>
                <DataTable
                  style={{ marginTop: '10px' }}
                  paginate={false}
                  bordered
                  columns={supplierProductInventoryTableColumns}
                  data={supplierInfo.products}
                />
              </TabPane>
            </Tabs>
          </div>
        </Content>
      </Layout>
    </>
  )
}
