{"compilerOptions": {"noEmit": true, "allowJs": true, "esModuleInterop": true, "target": "esnext", "module": "esnext", "moduleResolution": "node", "baseUrl": "./", "skipLibCheck": false, "rootDirs": ["./src", "../.redwood/types/mirror/web/src", "../api/src", "../.redwood/types/mirror/api/src"], "paths": {"src/*": ["./src/*", "../.redwood/types/mirror/web/src/*", "../api/src/*", "../.redwood/types/mirror/api/src/*"], "$api/*": ["../api/*"], "types/*": ["./types/*", "../types/*"], "@redwoodjs/testing": ["../node_modules/@redwoodjs/testing/web"]}, "typeRoots": ["../node_modules/@types", "./node_modules/@types"], "types": ["jest", "@testing-library/jest-dom"], "jsx": "preserve"}, "include": ["src", "../.redwood/types/includes/all-*", "../.redwood/types/includes/web-*", "../types", "./types"]}