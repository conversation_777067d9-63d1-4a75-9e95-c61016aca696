// Pass props to your component by passing an `args` object to your story
//
// ```tsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import EmissionsFactorSelector from './EmissionsFactorSelector'

const meta: Meta<typeof EmissionsFactorSelector> = {
  component: EmissionsFactorSelector,
  tags: ['autodocs'],
}

export default meta

type Story = StoryObj<typeof EmissionsFactorSelector>

export const Primary: Story = {}
