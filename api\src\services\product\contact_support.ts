import nodemailer from 'nodemailer'
import { GraphQLError } from 'graphql'

export const contactSupport = async ({ supportRequest }) => {
  try {
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.CONTACT_SUPPORT_EMAIL,
        pass: process.env.CONTACT_SUPPORT_PASSWORD,
      },
    })

    let mimeType = null,
      base64Data = null

    if (supportRequest.screenshot) {
      const base64SegmentData = supportRequest.screenshot.match(
        /^data:(.+);base64,(.+)$/
      )

      if (base64SegmentData.length == 3) {
        mimeType = base64SegmentData[1]
        base64Data = base64SegmentData[2]
      }
    }

    let mailOptions = {
      from: process.env.CONTACT_SUPPORT_EMAIL,
      to: process.env.CONTACT_SUPPORT_RECIPIENTS,
      subject: `Support Ticket: ` + supportRequest.subject,
      text: `User Name: ${supportRequest.name}\nUser Email: ${supportRequest.email}\n\n${supportRequest.description}`,
    }

    if (mimeType && base64Data) {
      mailOptions['attachments'] = [
        {
          filename: 'screenshot.png',
          content: Buffer.from(base64Data, 'base64'),
          encoding: 'base64',
          contentType: mimeType,
        },
      ]
    }

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.log(error)
        throw new Error(error)
      }

      console.log('Contact Support Email Sent: ' + info.response)
      return true
    })
  } catch (error) {
    let errorMessage = 'Error sending support email. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
