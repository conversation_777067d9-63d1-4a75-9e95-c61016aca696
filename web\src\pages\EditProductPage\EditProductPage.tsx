import { useParams } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'
import { Layout } from 'antd'
const { Content } = Layout
import AddProduct from 'src/components/AddProduct'

const EditProductPage = () => {
  const { productId, step } = useParams()
  return (
    <>
      <Metadata title="Edit Product" description="Edit Product page" />
      <Content>
        <Layout>
          <Content style={{ minHeight: 280 }}>
            <AddProduct editMode productId={productId} initialStep={step} />
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default EditProductPage
