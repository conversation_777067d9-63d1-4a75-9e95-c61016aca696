import { context } from '@redwoodjs/graphql-server'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const updateProductAttribute = async ({
  productId,
  productAttribute,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.put(
      `${process.env.LCA_API_ENDPOINT}/product-attributes/${tenantID}/${productId}`,
      sanitizeInput(productAttribute)
    )

    return {
      id: response.data.id,
      productId: response.data.product_id,
      key: response.data.key,
      value: response.data.value,
    }
  } catch (error) {
    let errorMessage = 'Error updating product attribute. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
