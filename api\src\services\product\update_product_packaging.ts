import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const updateProductPackaging = async ({
  packagingId,
  productPackaging,
}) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.put(
      `${
        process.env.LCA_API_ENDPOINT
      }/product-packaging/${tenantID}/${sanitizeInput(packagingId)}`,
      sanitizeInput(productPackaging)
    )

    return {
      material: response.data.packaging_material,
      component: response.data.packaging_item,
      packagingType: response.data.packaging_level,
      weight: response.data.weight_grams,
    }
  } catch (error) {
    let errorMessage = 'Error updating product packaging. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
