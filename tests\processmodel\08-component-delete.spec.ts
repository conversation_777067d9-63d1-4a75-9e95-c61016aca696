import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true
  }
})

test.use({ storageState: CONST.authFile })

test('Delete Components', async ({ page }) => {
  test.setTimeout(220000)

  page.on('response', async (response) => {
    if (response.url().includes('/graphql')) {
      try {
        const responseBody = await response.json()
        console.log('GraphQL Response:', JSON.stringify(responseBody))
      } catch (error) {
        console.error('Error parsing response:', error)
      }
    }
  })

  await page.goto(`${CONST.baseURL}/component`)

  await page.waitForSelector('text=Bottom Board', { timeout: 25000 })

  await page.getByRole('row', { name: 'Bottom Board' }).getByLabel('', { exact: true }).check();
  await page.getByRole('row', { name: 'Side Bar Left' }).getByLabel('', { exact: true }).check();
  await page.getByRole('row', { name: 'Side Bar Right' }).getByLabel('', { exact: true }).check();
  await page.getByRole('row', { name: 'Side Bar Top' }).getByLabel('', { exact: true }).check();
  await page.getByRole('row', { name: 'Table Top' }).getByLabel('', { exact: true }).check();
  await page.getByRole('row', { name: 'Wood Lining' }).getByLabel('', { exact: true }).check();

  await page.getByRole('button', { name: 'Action down' }).click();
  await page.getByRole('button', { name: 'Delete Selected component' }).click();
  await page.click('button#delete-component-modal-ok-button');

  await page.waitForSelector(
    `text=Component Bottom Board was deleted successfully`,
    { timeout: 10000 }
  )

  await page.waitForSelector(
    `text=Component Side Bar Left was deleted successfully`,
    { timeout: 10000 }
  )

  await page.waitForSelector(
    `text=Component Side Bar Right was deleted successfully`,
    { timeout: 10000 }
  )

  await page.waitForSelector(
    `text=Component Side Bar Top was deleted successfully`,
    { timeout: 10000 }
  )

  await page.waitForSelector(
    `text=Component Table Top was deleted successfully`,
    { timeout: 10000 }
  )

  await page.waitForSelector(
    `text=Component Wood Lining was deleted successfully`,
    { timeout: 10000 }
  )

})