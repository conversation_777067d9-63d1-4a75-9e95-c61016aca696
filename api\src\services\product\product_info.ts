import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { DEFAULT_IMPACT_FACTOR, sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const getProductInfo = async ({ productId, calculateEmissionsPerUnit = false }) => {
  try {
    const { urlSafeOrgName: tenantID } = context.currentUser.orgMemberInfo

    let _calculateEmissionsPerUnit = calculateEmissionsPerUnit || context.currentUser.metadata?.calculateEmissionsPerUnit;

    let impactFactor = DEFAULT_IMPACT_FACTOR
    if (context.currentUser.metadata?.defaultImpactFactor?.[tenantID]) {
      impactFactor = context.currentUser.metadata?.defaultImpactFactor?.[tenantID]
    }

    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/products/${tenantID}/${sanitizeInput(
        productId
      )}`
    )

    const productInfo = response.data

    if (!productInfo.hasOwnProperty('product')) {
      throw new Error('Unexpected response format from the server.')
    }

    const [processModelResponse, processModelWalkerResponse] =
      await Promise.all([
        axios.get(
          `${
            process.env.LCA_API_ENDPOINT
          }/v2/products/${tenantID}/${sanitizeInput(
            productInfo.product.product_id
          )}/process-model`
        ),
        axios.get(
          `${
            process.env.LCA_API_ENDPOINT
          }/v2/products/${tenantID}/${sanitizeInput(
            productInfo.product.product_id
          )}/process-model/walk`
        ),
      ])

    const processModelData = processModelResponse.data
    const processModelWalkerData = processModelWalkerResponse.data

    const mapNode = (node) => ({
      id: node.id,
      productId: node.product_id,
      name: node.name,
      component: node.component_name,
      description: node.description,
      packagingLevel: node.packaging_level,
      nodeType: node.node_type,
      amount:
        node.amount *
        (!['transportation'].includes(node.node_type)
          ? _calculateEmissionsPerUnit
            ? 1
            : productInfo.product.annual_sales_volume_units || 1
          : 1),
      unit: node.unit,
      quantity: node.quantity,
      scrapRate: node.scrap_rate ?? 0,
      scrapFate: node.scrap_fate,
      location: node.location
        ? {
            id: node.location.id,
            address1: node.location.address_1,
            address2: node.location.address_2,
            latitude: node.location.latitude,
            longitude: node.location.longitude,
            city: node.location.city,
            stateOrProvince: node.location.state_or_province,
            postalCode: node.location.postal_code,
            country: node.location.country,
          }
        : null,
      supplier: {
        id: node.supplier?.id,
        supplierName: node.supplier?.supplier_name,
      },
      emissionsFactor: node.emissions_factor
        ? {
            id: node.emissions_factor.id,
            activityName: node.emissions_factor.activity_name,
            geography: node.emissions_factor.geography,
            source: node.emissions_factor.source,
            activityType: node.emissions_factor.activity_type,
            referenceProduct: node.emissions_factor.reference_product,
            referenceProductAmount:
              node.emissions_factor.reference_product_amount,
            kgCO2e: node.emissions_factor.kg_co2e,
            unit: node.emissions_factor.unit,
            activityDescription: node.emissions_factor.activity_description,
          }
        : null,

      massAllocationPerKg: node.mass_allocation_per_kg ? true : false,
      recyclingDisposalRate: (node.eol_recycling_rate ?? 0) * 100,
      landfillDisposalRate: (node.eol_landfill_rate ?? 0) * 100,
      incinerationDisposalRate: (node.eol_incineration_rate ?? 0) * 100,
      compostingDisposalRate: (node.eol_composting_rate ?? 0) * 100,
    })

    const mapEdge = (edge) => ({
      productId: edge.product_id,
      fromNodeId: edge.from_node_id,
      toNodeId: edge.to_node_id,
    })

    const mapEmission = (emission) => ({
      name: emission.name,
      amount: emission.amount,
      unit: emission.unit,
      weight: emission.weight ?? 0,
      distance: emission.distance,
      wttEmissions: emission.wtt_emissions ?? 0,
      scrappageEmissions: emission.scrappage_emissions ?? 0,
      emissionsFactor: emission.emissions_factor
        ? {
            activityName: emission.emissions_factor.activity_name,
            referenceProduct: emission.emissions_factor.reference_product,
            geography: emission.emissions_factor.geography,
            source: emission.emissions_factor.source,
            kgCO2e: emission.emissions_factor.kg_co2e,
          }
        : null,
      totalEmissions: emission.total_emissions * (_calculateEmissionsPerUnit ? 1 : (productInfo.product.annual_sales_volume_units || 1)),
      recyclingEmissions: emission.recycling ?? 0,
      incinerationEmissions: emission.incineration ?? 0,
      landfillingEmissions: emission.landfilling ?? 0,
      compostingEmissions: emission.composting ?? 0,
    })

    const mapSegment = (segment) => {
      const gwpImpact = segment.impacts?.find(
        (impact) =>
          impact.emissions_factor_value?.impact_indicator?.lcia_method ===
            impactFactor.lciaMethod &&
          impact.emissions_factor_value?.impact_indicator?.category ===
            impactFactor.categoryName &&
          impact.emissions_factor_value?.impact_indicator?.indicator ===
            impactFactor.indicator
      )

      const node = processModelData.nodes.find(node => node.id === segment.node_id)
      return {
        id: segment.node_id,
        name: node.name,
        amount: segment.amount * (_calculateEmissionsPerUnit ? 1 : (productInfo.product.annual_sales_volume_units || 1)),
        unit: segment.unit,
        emissionsFactor: gwpImpact?.emissions_factor_value?.emissions_factor
          ? {
              activityName:
                gwpImpact.emissions_factor_value.emissions_factor.activity_name,
              referenceProduct:
                gwpImpact.emissions_factor_value.emissions_factor
                  .reference_product,
              geography:
                gwpImpact.emissions_factor_value.emissions_factor.geography,
              source: gwpImpact.emissions_factor_value.emissions_factor.source,
              kgCO2e: gwpImpact.emissions_factor_value.amount,
            }
          : null,
        totalEmissions: (gwpImpact?.impact_amount ?? 0) * (_calculateEmissionsPerUnit ? 1 : (productInfo.product.annual_sales_volume_units || 1)),
      }
    }

    // Function to extract all impact factors from a segment
    const extractAllImpactFactors = (segment) => {
      const node = processModelData.nodes.find(node => node.id === segment.node_id)

      const allImpacts = {}

      if (segment.impacts && segment.impacts.length > 0) {
        segment.impacts.forEach(impact => {
          if (impact.emissions_factor_value?.impact_indicator) {
            const indicator = impact.emissions_factor_value.impact_indicator
            const key = `${indicator.lcia_method}/${indicator.category}/${indicator.indicator}`

            allImpacts[key] = {
              lciaMethod: indicator.lcia_method,
              category: indicator.category,
              indicator: indicator.indicator,
              emissionsFactor: impact.emissions_factor_value?.emissions_factor
                ? {
                    activityName: impact.emissions_factor_value.emissions_factor.activity_name,
                    referenceProduct: impact.emissions_factor_value.emissions_factor.reference_product,
                    geography: impact.emissions_factor_value.emissions_factor.geography,
                    source: impact.emissions_factor_value.emissions_factor.source,
                    kgCO2e: impact.emissions_factor_value.amount,
                  }
                : null,
              totalEmissions: (impact.impact_amount ?? 0) * (_calculateEmissionsPerUnit ? 1 : (productInfo.product.annual_sales_volume_units || 1)),
            }
          }
        })
      }

      return {
        id: segment.node_id,
        name: node.name,
        amount: segment.amount * (_calculateEmissionsPerUnit ? 1 : (productInfo.product.annual_sales_volume_units || 1)),
        unit: segment.unit,
        impacts: allImpacts
      }
    }

    const mapWalkerCategory = (categoryData) => {
      if (!categoryData) return null

      const segments = categoryData.segment_nodes.map(mapSegment)
      const totalEmissions = segments.reduce(
        (sum, segment) => sum + (segment.totalEmissions ?? 0),
        0
      )
      return {
        totalEmissions,
        unit: segments[0]?.unit,
        emissions: segments,
      }
    }

    // Function to map walker category with all impact factors
    const mapWalkerCategoryWithAllImpactFactors = (categoryData) => {
      if (!categoryData) return null

      const segments = categoryData.segment_nodes.map(extractAllImpactFactors)

      return {
        unit: segments[0]?.unit,
        emissions: segments,
      }
    }

    const emissions = {}
    const pcrEmissions = []
    const allImpactFactorsEmissions = {}
    const pcrAllImpactFactorsEmissions = []

    Object.keys(processModelWalkerData['default']).forEach((nodeType) => {
      emissions[nodeType] = mapWalkerCategory(processModelWalkerData['default'][nodeType])
      allImpactFactorsEmissions[nodeType] = mapWalkerCategoryWithAllImpactFactors(processModelWalkerData['default'][nodeType])
    })

    if(context.currentUser.orgMemberInfo.orgMetadata?.pcr_categories?.[productInfo.product.primary_category]) {
      const pcrRule = context.currentUser.orgMemberInfo.orgMetadata?.pcr_categories?.[productInfo.product.primary_category]
      if (processModelWalkerData[pcrRule]) {
        Object.keys(processModelWalkerData[pcrRule]).forEach((nodeType) => {
          pcrEmissions.push(
            {
              sequenceNo: processModelWalkerData[pcrRule][nodeType].sequence_number,
              segmentName: nodeType,
              segmentEmissions: mapWalkerCategory(processModelWalkerData[pcrRule][nodeType])
            }
          )

          // Add PCR emissions with all impact factors
          pcrAllImpactFactorsEmissions.push(
            {
              sequenceNo: processModelWalkerData[pcrRule][nodeType].sequence_number,
              segmentName: nodeType,
              segmentEmissions: mapWalkerCategoryWithAllImpactFactors(processModelWalkerData[pcrRule][nodeType])
            }
          )
        })
      }
    }

    //sort pcr emisisons by sequence number
    pcrEmissions.sort((a, b) => a.sequenceNo - b.sequenceNo)

    return {
      productId: productInfo.product.product_id,
      productName: productInfo.product.product_name,
      productType: productInfo.product.product_type,
      brand: productInfo.product.brand,
      manufacturer: productInfo.product.manufacturer,
      contentWeight: productInfo.product.content_weight ?? 0,
      usesPerPack: productInfo.product.uses_per_package,
      weightUnit: productInfo.product.weight_unit,
      category: productInfo.product.primary_category,
      productImage: productInfo.product.product_image,
      countryOfUse: productInfo.product.country_of_use,
      factoryCountry: productInfo.product.factory_locale?.country,
      factoryCity: productInfo.product.factory_locale?.city,
      annualSalesVolumeUnits: productInfo.product.annual_sales_volume_units,
      tags: productInfo.product.tags?.split(',').map((tag) => tag.trim()),
      functionalUnit: productInfo.product.functional_unit,
      packageType: productInfo.product.package_type,
      clonedFromProductId: productInfo.product.cloned_from_product_id,
      nodes: processModelData.nodes.map(mapNode),
      edges: processModelData.edges.map(mapEdge),
      emissions,
      pcrEmissions: pcrEmissions,
      allImpactFactorsEmissions,
      pcrAllImpactFactorsEmissions,
    }
  } catch (error) {
    let errorMessage = 'Error fetching product info. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
