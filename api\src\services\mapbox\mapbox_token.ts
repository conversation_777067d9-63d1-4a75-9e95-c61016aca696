import { context } from '@redwoodjs/graphql-server'
import axios from 'axios'
const mbxClient = require('@mapbox/mapbox-sdk')
const mbxTokens = require('@mapbox/mapbox-sdk/services/tokens')
import moment from 'moment'

const mapboxSecretToken = process.env.MAPBOX_SECRET_TOKEN
const baseClient = mbxClient({
  accessToken: mapboxSecretToken,
})

const tokensService = mbxTokens(baseClient)

const tokenExpiry = 2 //hours

const mapboxAccountUsernmame =
  process.env.MAPBOX_ACCOUNT_USERNAME ?? 'carbonbright'

function isTokenActive(token) {
  return moment(token.created).add(tokenExpiry, 'hours').isAfter(moment())
}

async function invalidateToken(tokenId) {
  try {
    const response = await axios.delete(
      `https://api.mapbox.com/tokens/v2/${mapboxAccountUsernmame}/${tokenId}?access_token=${mapboxSecretToken}`
    )

    if (response.status === 204) {
      console.log('Mapbox token invalidated')
    }
  } catch (error) {
    console.error('Error deleting mapbox token: ', error.message)
  }
}

function getExpiredTokens(tokens) {
  return tokens.filter((token) =>
    moment(token.created).add(tokenExpiry, 'hours').isBefore(moment())
  )
}

function getScopedUrl(env) {
  if (env.includes('prod')) {
    return 'https://app.carbonbright.co'
  } else {
    return 'https://app.carbonbright.io'
  }
}

function getEnv(origin) {
  try {
    const url = new URL(origin);
    const host = url.host;
    const allowedHosts = {
      'carbonbright.co': 'prod',
      'carbonbright.io': 'staging'
    };
    return allowedHosts[host] || 'dev';
  } catch (error) {
    console.error('Invalid URL:', error.message);
    return 'dev';
  }
}

async function getAccessToken(tenantID) {
  try {
    const response = await tokensService.listTokens().send()
    const tokens = response.body

    const tenantTokens = tokens.filter((token) => token.note === tenantID)
    const expiredTokens = getExpiredTokens(tenantTokens)

    for (const token of expiredTokens) {
      console.log('Deleting mapbox token: ', token.id)
      invalidateToken(token.id)
    }

    const activeToken = tenantTokens.find(isTokenActive)

    return activeToken || (await createToken(tenantID))
  } catch (error) {
    console.error('Error fetching mapbox tokens: ', error.message)
    throw error
  }
}

// Note: Remove/Disable 'allowedUrls' when testing locally
async function createToken(tenantID) {
  try {
    const response = await tokensService
      .createToken({
        scopes: ['styles:read', 'fonts:read'],
        allowedUrls: tenantID.includes('dev') ? null : [getScopedUrl(tenantID)],
        note: tenantID,
      })
      .send()

    const token = response.body
    return token
  } catch (error) {
    console.error('Error creating mapbox token: ', error.message)
    throw error
  }
}

// Note/Caveat: You cannot use 'allowedUrls' when creating a temporary token
async function createTemporaryToken() {
  try {
    const response = await tokensService
      .createTemporaryToken({
        scopes: ['styles:read', 'fonts:read'],
        expires: moment().add(5, 'minutes').toISOString(),
      })
      .send()

    const token = response.body
    return token
  } catch (error) {
    console.error('Error creating mapbox token: ', error.message)
    throw error
  }
}

export const getMapboxAccessToken = async () => {
  try {
    const tenantID = `tenant_${context.currentUser.orgMemberInfo.orgName}`

    const env = getEnv(context.event.headers.origin)

    const token = await getAccessToken(`${tenantID}_${env}`)

    return {
      accessToken: token.token,
      expiresAt: moment(token.created).add(tokenExpiry, 'hours').toISOString(),
    }
  } catch (error) {
    console.error('Error fetching mapbox token:', error)
    return null
  }
}
