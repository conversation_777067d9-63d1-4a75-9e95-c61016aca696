import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const createIntermediateExchange = async ({ intermediateExchange }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.post(
      `${process.env.LCA_API_ENDPOINT}/emissions-factors/${tenantID}/exchange`,
      sanitizeInput(intermediateExchange)
    )

    return {
      id: response.data.id,
      exchangeName: response.data.exchange_name,
      amount: response.data.amount,
      unit: response.data.unit,
    }
  } catch (error) {
    let errorMessage = 'Error creating intermediate exchange. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
