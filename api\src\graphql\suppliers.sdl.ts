export const schema = gql`
  type RawMaterialEmissions {
    ingredientName: String!
    totalEmissions: Float!
    ingredientEmissions: Float!
  }

  type PackagingEmissions {
    packageMaterial: String!
    totalEmissions: Float!
  }

  type IngredientDistributionEmissions {
    ingredientName: String!
    totalEmissions: Float!
    transportEmissions: Float!
  }

  type PackagingDistributionEmissions {
    packageMaterial: String!
    totalEmissions: Float!
    transportEmissions: Float!
  }

  type Product {
    productName: String!
    productId: String!
    category: String!
    brand: String!
    annualSalesVolumeUnits: Int!
    rawMaterialEmissions: [RawMaterialEmissions]
    packagingEmissions: [PackagingEmissions]
    ingredientDistributionEmissions: [IngredientDistributionEmissions]
    packagingDistributionEmissions: [PackagingDistributionEmissions]
    totalEmissions: Float
    scope3Emissions: Float
  }

  type Supplier {
    supplierName: String
    supplierId: Int!
    supplierLevel: Int!
    supplierType: String
    country: String
    city: String
    address: String
    totalProducts: Int
    products: [Product]
    scope3Emissions: Float
  }

  type Query {
    getSuppliers: [Supplier] @requireAuth
  }
`
