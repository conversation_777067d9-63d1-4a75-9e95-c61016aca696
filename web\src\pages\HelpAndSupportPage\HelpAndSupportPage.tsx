import { Link, routes } from '@redwoodjs/router'
import { MetaTags } from '@redwoodjs/web'

const HelpAndSupportPage = () => {
  return (
    <>
      <MetaTags title="HelpAndSupport" description="HelpAndSupport page" />

      <h1>HelpAndSupportPage</h1>
      <p>
        Find me in{' '}
        <code>./web/src/pages/HelpAndSupportPage/HelpAndSupportPage.tsx</code>
      </p>
      <p>
        My default route is named <code>helpAndSupport</code>, link to me with `
        <Link to={routes.helpAndSupport()}>HelpAndSupport</Link>`
      </p>
    </>
  )
}

export default HelpAndSupportPage
