.FileViewer__container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px 0;
  padding: 10px;
  max-height: 500px;
  overflow-y: scroll;
}

.FileViewer__container__load {
  margin-top: 1em;
  color: white;
}

.FileViewer__container__document {
  width: 100%;
  max-width: calc(100% - 2em);
  margin: 1em 0;
}

.FileViewer__container__document .react-pdf__Document {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.FileViewer__container__document .react-pdf__Page {
  margin: 1em 0;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
}

.FileViewer__container__document .react-pdf__message {
  padding: 20px;
  color: white;
}