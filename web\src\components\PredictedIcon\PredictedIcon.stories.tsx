// Pass props to your component by passing an `args` object to your story
//
// ```jsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import PredictedIcon from './PredictedIcon'

const meta: Meta<typeof PredictedIcon> = {
  component: PredictedIcon,
}

export default meta

type Story = StoryObj<typeof PredictedIcon>

export const Primary: Story = {}
