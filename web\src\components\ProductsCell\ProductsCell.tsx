import type { ProductsQuery } from 'types/graphql'
import {
  type CellSuccessProps,
  type CellFailureProps,
  useQuery,
  useMutation,
} from '@redwoodjs/web'
import {
  Avatar,
  Checkbox,
  message,
  notification,
  Tag,
  Tooltip,
  Tour,
  TourProps,
} from 'antd'
import {
  TagOutlined,
  FilePdfOutlined,
  CopyOutlined,
  ApiOutlined,
  WarningOutlined,
} from '@ant-design/icons'
import DataTable from '../DataTable/DataTable'
import { navigate, routes } from '@redwoodjs/router'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import { useEffect, useMemo, useState } from 'react'
import './style.css'
import ErrorHandler from '../ErrorHandler/ErrorHandler'
import InteractiveTagComponent from '../InteractiveTagComponent/InteractiveTagComponent'
import { saveAs } from 'file-saver'
import LoadingSkeleton from '../LoadingSkeleton/LoadingSkeleton'
import { renderImpactFactorUnit } from 'src/utils/helper'

export const QUERY = gql`
  query ProductsQuery {
    getProducts {
      productName
      productId
      brand
      category
      imageUrl
      materialEmissions
      totalEmissions
      clonedFromProductId
      tags
      createdAt
      updatedAt
    }
  }
`

const UPDATE_PRODUCT_TAGS = gql`
  mutation UpdateProductTags($productId: String!, $tags: [String!]!) {
    updateProductTags(productId: $productId, tags: $tags)
  }
`

const LOG_PRODUCT_TOUR_STATUS = gql`
  mutation LogProductTourStatus($tourCompleted: Boolean!) {
    logProductTourStatus(tourCompleted: $tourCompleted)
  }
`

export const Loading = () => <LoadingSkeleton />

export const Failure = ({ error }: CellFailureProps) => (
  <ErrorHandler error={error} />
)

export const Success = ({ getProducts }: CellSuccessProps<ProductsQuery>) => {
  if (!getProducts) {
    return <ErrorHandler />
  }

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const tenantID =
    orgMemberInfo && orgMemberInfo.urlSafeOrgName
      ? orgMemberInfo.urlSafeOrgName
      : null

  const sortProducts = (products) => {
    return products
      .map((product, index) => ({
        key: index + 1,
        ...product,
        originalIndex: index,
      }))
      .sort((a, b) => {
        const dateA = new Date(a.updatedAt)
        const dateB = new Date(b.updatedAt)

        return dateB - dateA
      })
  }

  const {
    refetch: refetchProducts,
    data: refetchedData,
    loading: refetchedDataIsLoading,
  } = useQuery(QUERY)
  const [selectedProducts, setSelectedProducts] = useState([])
  const [tableData, setTableData] = useState(sortProducts(getProducts))
  const [tableKey, setTableKey] = useState(Date.now())
  const [filteredData, setFilteredData] = useState(tableData)

  useEffect(() => {
    if (refetchedData) {
      setTableData(sortProducts(refetchedData.getProducts))
      setTableKey(Date.now())
    }
  }, [refetchedData])

  const handlerefetchProducts = async () => {
    setSelectedProducts([])
    refetchProducts()
  }

  const [logProductTourStatus] = useMutation(LOG_PRODUCT_TOUR_STATUS)
  const [updateProductTags, { loading: updateProductTagsIsLoading }] =
    useMutation(UPDATE_PRODUCT_TAGS)
  const onProductTourCompleted = async () => {
    await logProductTourStatus({
      variables: { tourCompleted: true },
    })
  }

  const onCheckboxChange = (record, checked) => {
    if (!checked) {
      setSelectedProducts((prevSelected) =>
        prevSelected.filter((product) => product.productId !== record.productId)
      );
      return;
    }

    setSelectedProducts((prevSelected) => {
      if (prevSelected.some(item => item.productId === record.productId)) {
        return prevSelected;
      }

      const updated = [...prevSelected, {
        productId: record.productId,
        productName: record.productName,
        clonedFromProductId: record.clonedFromProductId,
      }];
      return updated;
    });
  };

  const exportToCsv = (dataToExport) => {
    if (dataToExport.length === 0) {
      notification.warning({
        message: 'No data to export',
        description: 'There are no products available to export.',
      })
      return
    }

    const headers = [
      'Product ID',
      'Product',
      'Category',
      'Brand',
      `Raw Material Carbon Footprint (${renderImpactFactorUnit(userMetadata)})`,
      `Total Carbon Footprint (${renderImpactFactorUnit(userMetadata)})`,
      'Tags',
    ]

    const csvData = [headers]

    dataToExport.forEach((product) => {
      const row = [
        product.productId,
        product.productName,
        product.category,
        product.brand,
        product.materialEmissions,
        product.totalEmissions,
        `"${product.tags.join(',').replace(/"/g, '""')}"`,
      ]
      csvData.push(row)
    })

    const blob = new Blob([csvData.map((row) => row.join(',')).join('\n')], {
      type: 'text/csv;charset=utf-8',
    })
    saveAs(blob, 'CarbonBright_Product_Inventory.csv')
  }

  const handleSave = async (updatedProduct) => {
    try {
      await updateProductTags({
        variables: {
          productId: updatedProduct.productId,
          tags: updatedProduct.tags,
        },
      })

      await handlerefetchProducts()

      const newProducts = tableData.map((product) =>
        product.productId === updatedProduct.productId
          ? { ...product, ...updatedProduct }
          : product
      )

      setTableData(sortProducts(newProducts))
      setTableKey(Date.now())
    } catch (error) {
      console.error(
        `Failed to update tags for product ${updatedProduct.productName}`,
        error
      )
      message.error('Failed to update tags for product')
    }
  }

  const productAddItems = [
    {
      label: 'Add single product',
      icon: <TagOutlined />,
      onClick: () => navigate(routes.addProduct()),
      id: 'add-single-product-button',
      isPro: true,
      upgradeMessage:
        'You have reached the maximum number of products allowed in the trial plan. Upgrade to add more products.',
    },
    {
      label: 'Add single product (File)',
      icon: <FilePdfOutlined />,
      onClick: () => navigate(routes.addProductImportFile()),
      id: 'add-product-file-import-button',
      isPro: true,
      upgradeMessage:
        'You have reached the maximum number of products allowed in the trial plan. Upgrade to upload more products.',
    },
    {
      label: 'Bulk Upload',
      icon: <CopyOutlined />,
      id: 'bulk-upload-button',
      isPro: true,
      disabled: true,
      upgradeMessage: 'Bulk upload is a PRO feature. Please upgrade your plan.',
    },
    {
      label: 'API Integration',
      icon: <ApiOutlined />,
      id: 'api-integration-button',
      isPro: true,
      disabled: true,
      upgradeMessage:
        'API integration is a PRO feature. Please upgrade your plan.',
    },
  ]

  const handleSelectAll = (checked) => {
    if (checked) {
      // Get all product/component IDs from the filtered data
      const allFilteredItems = filteredData.map(item => ({
        productId: item.productId,
        productName: item.productName,
        clonedFromProductId: item.clonedFromProductId
      }));
      setSelectedProducts(allFilteredItems);
    } else {
      // Clear all selections
      setSelectedProducts([]);
    }
  };

  const columns = useMemo(
    () => [
      {
        title: () => (
          <Checkbox
            indeterminate={selectedProducts.length > 0 && selectedProducts.length < filteredData.length}
            checked={filteredData.length > 0 && selectedProducts.length === filteredData.length}
            onChange={(e) => handleSelectAll(e.target.checked)}
          />
        ),
        dataIndex: 'productName',
        render: (key, record) => (
          <Checkbox
            value={key}
            checked={selectedProducts.some(item => item.productId === record.productId)}
            onClick={(e) => e.stopPropagation()}
            onChange={(e) => onCheckboxChange(record, e.target.checked)}
          />
        ),
        width: '1%',
      },
      {
        title: '',
        dataIndex: 'imageUrl',
        render: (imageUrl: string) => (
          <Avatar shape="square" size={50} src={imageUrl} />
        ),
        width: '5%',
      },
      {
        title: 'Product Name',
        dataIndex: 'productName',
        sorter: true,
        width: '15%',
      },
      {
        title: 'Product ID',
        dataIndex: 'productId',
        sorter: true,
        width: '5%',
      },
      {
        title: 'Category',
        dataIndex: 'category',
        sorter: true,
        width: '15%',
      },
      {
        title: 'Total Impact',
        dataIndex: 'totalEmissions',
        render: (text: string, record) => {
          const emissions = parseFloat(text)
          if (emissions === 0) {
            return (
              <Tooltip title="Emissions calculation failed. The product may have issues with its data.">
                <span>
                  <WarningOutlined style={{ color: '#faad14', marginRight: '8px' }} />
                  <Tag color="red">{emissions.toFixed(4)} {renderImpactFactorUnit(userMetadata)}</Tag>
                </span>
              </Tooltip>
            )
          }
          return (
            <Tag color="blue">{emissions.toFixed(4)} {renderImpactFactorUnit(userMetadata)}</Tag>
          )
        },
        sorter: true,
        width: '10%',
      },
      {
        title: 'Brand',
        dataIndex: 'brand',
        sorter: true,
        width: '10%',
      },
      {
        title: 'Tags',
        dataIndex: 'tags',
        key: 'tags',
        render: (tags, record) => (
          <InteractiveTagComponent
            record={record}
            handleSave={handleSave}
            data={tableData}
            loading={updateProductTagsIsLoading}
          />
        ),
        width: '15%',
        filters: (() => {
          const allTags = Array.from(
            new Set(tableData.flatMap((product) => product.tags || []))
          )
          return allTags.map((tag) => ({ text: tag, value: tag }))
        })(),
        filterSearch: true,
      },
    ],
    [tableData, handleSave, selectedProducts, onCheckboxChange]
  )

  const [isTourOpen, setIsTourOpen] = useState(
    !(userMetadata.user.metadata?.milestone_productTour ?? false)
  )

  const guidedTourSteps: TourProps['steps'] = [
    {
      title: (
        <>
          <p style={{ fontSize: '18px' }}>Let's Create Your First Product!</p>
        </>
      ),
      description: (
        <>
          <p style={{ fontSize: '16px', marginTop: '15px' }}>
            Click on the Add Product button to get started!
          </p>
          <img
            style={{
              marginLeft: '25%',
              marginTop: '15px',
              width: 'auto',
              border: '1px solid grey',
              borderRadius: '5px',
            }}
            src="/images/tour/tour-6.png"
          />
        </>
      ),
      nextButtonProps: {
        className: 'animate-pulse',
        children: 'Next',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: '#f3c314d4',
        },
      },
      target: () => document.getElementById('add-product-dropdown-button'),
    },
    {
      title: (
        <>
          <p style={{ fontSize: '18px' }}>Let's Create Your First Product!</p>
        </>
      ),
      description: (
        <>
          <p style={{ fontSize: '16px', marginTop: '15px' }}>
            You can also import a product from a PDF file by clicking on Add
            Single Product (File) option from the Add Product dropdown.
          </p>
          <img
            style={{
              marginTop: '15px',
              width: 'auto',
              border: '1px solid grey',
              borderRadius: '5px',
            }}
            src="/images/tour/tour-7.png"
          />
        </>
      ),
      prevButtonProps: {
        children: 'Back',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: 'white',
        },
      },
      nextButtonProps: {
        children: 'Finish',
        style: {
          fontWeight: 600,
          width: '100px',
          height: '30px',
          backgroundColor: 'white',
        },
      },
      target: () => document.getElementById('add-product-dropdown-button'),
    },
  ]

  const handleRowClick = (record, index, event) => {
    const clickedCell = event.target.closest('td')
    const row = clickedCell.parentElement
    const isFirstColumn = clickedCell === row.firstElementChild
    const isLastColumn = clickedCell === row.lastElementChild

    if (!isFirstColumn && !isLastColumn) {
      navigate(
        routes.productDetail({
          tenantID: tenantID,
          productId: encodeURIComponent(record.productId),
        })
      )
    }
  }

  return (
    <>
      <Tour
        type={'default'}
        open={isTourOpen}
        onClose={() => setIsTourOpen(false)}
        steps={guidedTourSteps}
        onFinish={onProductTourCompleted}
      />
      <DataTable
        id={'products-table'}
        key={tableKey}
        data={tableData}
        columns={columns}
        selectedItems={selectedProducts}
        refetch={handlerefetchProducts}
        searchable
        filters
        onRow={(record, index) => ({
          onClick: (event) => handleRowClick(record, index, event),
          className: `clickable-row ${selectedProducts.some(item => item.productId === record.productId) ? 'selected-row' : ''}`,
        })}
        tableType="product"
        addItems={productAddItems}
        exportToCsv={exportToCsv}
        _filteredData={filteredData}
        onDataFiltered={(newFilteredData) => setFilteredData(newFilteredData)}
        tableLoading={refetchedDataIsLoading}
      />
    </>
  )
}
