import { Link, routes, useParams } from '@redwoodjs/router'
import { MetaTags } from '@redwoodjs/web'
import { Layout } from 'antd'
const { Content } = Layout
import CompareProducts from 'src/components/CompareProducts/CompareProducts'

const CompareProductPage = () => {
  const { productId, clonedProductId } = useParams()

  return (
    <>
      <MetaTags title="Compare Product" description="Compare Product page" />
      <Content>
        <Layout>
          <Content style={{ minHeight: 280 }}>
            <CompareProducts
              productId={productId}
              clonedProductId={clonedProductId}
            />
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default CompareProductPage
