import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true
  }
})

test.use({ storageState: CONST.authFile })

test('Impact Category Selector', async ({ page }) => {
  test.setTimeout(220000)

  await page.setViewportSize({ width: 1920, height: 1080 });

  await page.goto(`${CONST.baseURL}/products`)

  await page.getByLabel('setting').locator('svg').click();
  await page.getByText('Climate Change (TRACI 2.1 GWP').click();
  await page.getByText('Ozone Depletion (TRACI 2.1)').click();

  await expect(page.locator('tbody')).toContainText('kg CFC-11-Eq', { timeout: 10000 });

  await page.getByLabel('setting').locator('svg').click();
  await page.getByText('Ozone Depletion (TRACI 2.1)').click();
  await page.getByText('Climate Change (TRACI 2.1 GWP').click();

  await expect(page.locator('tbody')).toContainText('kg CO2-Eq', { timeout: 10000 });

})