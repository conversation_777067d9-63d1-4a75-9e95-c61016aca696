import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const predictConsumerUse = async ({ productCategory, geographyIso3 }) => {
  try {
    const response = await axios.get(
      `${
        process.env.LCA_API_ENDPOINT
      }/consumer-use/predict-process-model/${sanitizeInput(
        geographyIso3
      )}/${sanitizeInput(productCategory)}`
    )

    return response.data.nodes.map((consumerUseNode) => {
      return {
        name: consumerUseNode.name,
        amount: consumerUseNode.amount,
        unit: consumerUseNode.unit,
      }
    })
  } catch (error) {
    console.log('Error predicting consumer use:', error)
    let errorMessage = 'Error predicting consumer use. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
