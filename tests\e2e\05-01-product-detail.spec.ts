import { test, expect } from '@playwright/test'
import { CONST } from '../const'
import state from '../state'

test.beforeEach(() => {
  if (state.hasFailed) {
    test.skip(true, 'Skipping due to a previous test failure')
  }
})

test.afterEach(async ({ page }, testInfo) => {
  if (['failed', 'timedOut'].indexOf(testInfo.status ?? '') !== -1) {
    state.hasFailed = true;
  }
})

test.use({ storageState: CONST.authFile })

test('Product Count', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  const productCount = await page.$$eval(
    'table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(productCount).toBeGreaterThanOrEqual(1)
})

test('Product Detail', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially(CONST.testProduct.productName, { delay: 100 })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible()

  await productInfo.locator('td:nth-child(3)').click()

  await page.waitForSelector(`text=${CONST.testProduct.productName}`)

  const productCarbonFootprint =
    (await page
      .locator(
        'xpath=/html/body/div/div/div/main/div/main/div/main/div[3]/main/div[2]/div/div[2]/div/div/div[1]/div[2]/div/div/table/tbody/tr[1]/td/span/b'
      )
      .textContent()) ?? ''

  expect(isNaN(Number(productCarbonFootprint))).toBe(false)

  const lifeCycleEmissionsDonutChart = page.locator(
    'div[data-chart-source-type="G2Plot"]'
  )

  await expect(lifeCycleEmissionsDonutChart).toBeVisible()

  await page.getByRole('tab', { name: 'Product Details' }).click()

  const brand =
    (await page
      .locator(
        'xpath=/html/body/div/div/div/main/div/main/div/main/div[3]/main/div[2]/div/div[2]/div/div[2]/div/div[1]/div[2]/div/div/div/table/tbody/tr[2]/td/span'
      )
      .textContent()) ?? ''

  expect(brand).toEqual(CONST.testProduct.brand)

  const category =
    (await page
      .locator(
        'xpath=/html/body/div/div/div/main/div/main/div/main/div[3]/main/div[2]/div/div[2]/div/div[2]/div/div[1]/div[2]/div/div/div/table/tbody/tr[3]/td/span'
      )
      .textContent()) ?? ''

  expect(category).toEqual(CONST.testProduct.category)

  const countryOfOrigin =
    (await page
      .locator(
        'xpath=/html/body/div/div/div/main/div/main/div/main/div[3]/main/div[2]/div/div[2]/div/div[2]/div/div[1]/div[2]/div/div/div/table/tbody/tr[4]/td/span'
      )
      .textContent()) ?? ''

  expect(countryOfOrigin).toEqual(CONST.testProduct.countryOfOrigin)


  const functionalUnit =
  (await page
    .locator(
      'xpath=/html/body/div/div/div/main/div/main/div/main/div[3]/main/div[2]/div/div[2]/div/div[2]/div/div[1]/div[2]/div/div/div/table/tbody/tr[7]/td/span'
    )
    .textContent()) ?? ''

  expect(functionalUnit).toEqual("1L")

  await page.getByRole('tab', { name: 'Lifecycle Inventory Data' }).click()

  const rawMaterialsCount = await page.$$eval(
    'div#rawMaterialsIngredients table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(rawMaterialsCount).toBe(CONST.testProduct.ingredients.length - 1)

  const transportationTable = await page.$$(
    'div#transportationTable table tr.ant-table-row.ant-table-row-level-0 td:nth-child(1)'
  )
  const transportationTableText = await Promise.all(
    transportationTable.map(async (el) => {
      return (await el.textContent())?.trim() ?? ''
    })
  )
  transportationTableText.forEach((text) => {
    expect(text).not.toContain('water')
  })

  await page.getByRole('button', { name: 'right Manufacturing edit Edit' }).click();

  const manufacturingProceessCount = await page.$$eval(
    'div#manufacturingTable table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(manufacturingProceessCount).toBe(1)

})

test('Product Detail APFF', async ({ page }) => {
  test.setTimeout(240000)
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page.screenshot({
    path: 'tests/screenshots/product_inventory.png',
  })

  await page.waitForTimeout(5000)

  await page.screenshot({
    path: 'tests/screenshots/product_inventory_post_timeout.png',
  })

  await page
    .locator('input#search-input')
    .pressSequentially('Vibrant Colors Laundry Plus', { delay: 100 })

  await page.screenshot({
    path: 'tests/screenshots/product_inventory_port_search.png',
  })


  await page.waitForTimeout(5000)

  await page.screenshot({
    path: 'tests/screenshots/product_inventory_port_search.png',
  })

  const productInfo = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(productInfo).toBeVisible({ timeout: 5000 })

  await productInfo.locator('td:nth-child(3)').click()

  await page.waitForSelector(`text=Vibrant Colors Laundry Plus`)

  const productCarbonFootprint =
    (await page
      .locator(
        'xpath=/html/body/div/div/div/main/div/main/div/main/div[3]/main/div[2]/div/div[2]/div/div/div[1]/div[2]/div/div/table/tbody/tr[1]/td/span/b'
      )
      .textContent()) ?? ''

  expect(isNaN(Number(productCarbonFootprint))).toBe(false)

  const lifeCycleEmissionsDonutChart = page.locator(
    'div[data-chart-source-type="G2Plot"]'
  )

  await expect(lifeCycleEmissionsDonutChart).toBeVisible()

  await page.getByRole('tab', { name: 'Product Details' }).click()

  const countryOfOrigin =
    (await page
      .locator(
        'xpath=/html/body/div/div/div/main/div/main/div/main/div[3]/main/div[2]/div/div[2]/div/div[2]/div/div[1]/div[2]/div/div/div/table/tbody/tr[4]/td/span'
      )
      .textContent()) ?? ''

  expect(countryOfOrigin).toEqual('United States')

  const functionalUnit =
    (await page
      .locator(
        'xpath=/html/body/div/div/div/main/div/main/div/main/div[3]/main/div[2]/div/div[2]/div/div[2]/div/div[1]/div[2]/div/div/div/table/tbody/tr[7]/td/span'
      )
      .textContent()) ?? ''

  expect(functionalUnit).toEqual("1L")

  await page.getByRole('tab', { name: 'Lifecycle Inventory Data' }).click()

  const rawMaterialsCount = await page.$$eval(
    'div#rawMaterialsIngredients table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(rawMaterialsCount).toBeGreaterThanOrEqual(1)

  await page.getByRole('button', { name: 'right Manufacturing edit Edit' }).click();

  const manufacturingProceessCount = await page.$$eval(
    'div#manufacturingTable table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(manufacturingProceessCount).toBe(1)
})

// test('Download Report', async ({ page }) => {
//   await page.goto(`${CONST.baseURL}/products`)
//   await page.waitForSelector('text=Product Name')
//   await page.context().storageState({ path: CONST.authFile })

//   await page
//     .locator('input#search-input')
//     .pressSequentially(`${CONST.testProduct.productName} (COPY)`, {
//       delay: 100,
//     })

//   const productInfo = page
//     .locator('table tr.ant-table-row.ant-table-row-level-0')
//     .first()

//   expect(productInfo).toBeVisible()

//   await productInfo.locator('td:nth-child(3) a').click()

//   await page.waitForSelector(`text=${CONST.testProduct.productName}`)

//   await page.getByRole('tab', { name: 'Lifecycle Inventory Data' }).click()

//   const rawMaterialInfo = page
//     .locator(
//       'div#rawMaterialsIngredients table tr.ant-table-row.ant-table-row-level-0'
//     )
//     .first()

//   const weight =
//     (await rawMaterialInfo.locator('td:nth-child(2)').textContent()) ?? ''

//   expect(weight).toContain(
//     CONST.testProduct.ingredients[1].weightToBeUpdated ?? ''
//   )
// })

// test('Verify Edit Cloned Product', async ({ page }) => {
//   test.setTimeout(120000)

//   await page.goto(`${CONST.baseURL}/products`)
//   await page.waitForSelector('text=Product Name')
//   await page.context().storageState({ path: CONST.authFile })

//   await page
//     .locator('input#search-input')
//     .pressSequentially(`${CONST.testProduct.productName} (COPY)`, {
//       delay: 100,
//     })

//   const productInfo = page
//     .locator('table tr.ant-table-row.ant-table-row-level-0')
//     .first()

//   expect(productInfo).toBeVisible()

//   await productInfo.locator('td:nth-child(3)').click()

//   await page.waitForSelector(`text=${CONST.testProduct.productName}`)

//   await page.getByRole('tab', { name: 'Lifecycle Inventory Data' }).click()

//   const rawMaterialInfo = page
//     .locator(
//       'div#rawMaterialsIngredients table tr.ant-table-row.ant-table-row-level-0'
//     )
//     .first()

//   const weight =
//     (await rawMaterialInfo.locator('td:nth-child(2)').textContent()) ?? ''

//   expect(weight).toContain(
//     CONST.testProduct.ingredients[1].weightToBeUpdated ?? ''
//   )
// })

test('Compare Product', async ({ page }) => {
  await page.goto(`${CONST.baseURL}/products`)
  await page.waitForSelector('text=Product Name')
  await page.context().storageState({ path: CONST.authFile })

  await page
    .locator('input#search-input')
    .pressSequentially(CONST.testProduct.productName, { delay: 100 })

  const primaryProduct = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .first()

  expect(primaryProduct).toBeVisible()

  const selectPrimaryProductCheckbox = await primaryProduct.locator(
    'td:nth-child(1) input'
  )

  await selectPrimaryProductCheckbox.check()

  const clonedProduct = page
    .locator('table tr.ant-table-row.ant-table-row-level-0')
    .nth(1)

  expect(clonedProduct).toBeVisible()

  const selectClonedProductCheckbox = await clonedProduct.locator(
    'td:nth-child(1) input'
  )

  await selectClonedProductCheckbox.check()

  await page.click('button#action-dropdown-button')
  await page.click('button#compare-product-button')

  await page.waitForSelector(`text=${CONST.testProduct.productName}`)

  const lifecycleInventoryDataTable = await page.$$eval(
    'table tr.ant-table-row.ant-table-row-level-0',
    (rows) => rows.length
  )

  expect(lifecycleInventoryDataTable).toBeGreaterThan(6)

  const productComparisonProgressBar = await page.$$(
    'div.tremor-ProgressBar-root.flex.items-center.w-full'
  )

  expect(productComparisonProgressBar.length).toBe(2)
})
