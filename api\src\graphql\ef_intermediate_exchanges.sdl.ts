export const schema = gql`

  type IntermediateExchangeEmissionsFactor {
    id: Int!
    activityName: String!
    referenceProduct: String!
    geography: String!
    source: String!
    amount: Float
    unit: String
  }

  type IntermediateExchange {
    id: Int!
    exchangeName: String!
    amount: Float!
    unit: String!
    exchangeEmissionsFactor: IntermediateExchangeEmissionsFactor
    parentEmissionsFactor: IntermediateExchangeEmissionsFactor
  }

  type Query {
    getEFIntermediateExchanges(
      activityName: String!
      referenceProduct: String!
      geography: String!
      source: String!
      sharedScope: Boolean
    ): [IntermediateExchange] @requireAuth
  }
`
