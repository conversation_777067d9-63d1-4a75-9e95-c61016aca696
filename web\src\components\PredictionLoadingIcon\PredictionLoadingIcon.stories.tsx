// Pass props to your component by passing an `args` object to your story
//
// ```jsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import PredictionLoadingIcon from './PredictionLoadingIcon'

const meta: Meta<typeof PredictionLoadingIcon> = {
  component: PredictionLoadingIcon,
}

export default meta

type Story = StoryObj<typeof PredictionLoadingIcon>

export const Primary: Story = {}
