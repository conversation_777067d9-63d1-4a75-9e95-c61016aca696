import { Metadata } from '@redwoodjs/web'
import { Layout, Menu, Tabs, Card, Typography, Button, List, Space } from 'antd'
import {
  ShoppingCartOutlined,
  SearchOutlined,
  UserOutlined,
} from '@ant-design/icons'

const { Header, Content } = Layout
const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs
import { CheckCircleOutlined } from '@ant-design/icons'
import { useParams } from '@redwoodjs/router'
import { routes } from '@redwoodjs/router'
import ProductSustainabilityWidget from 'src/components/ProductSustainabilityWidget/ProductSustainabilityWidget'

const ProductSustainabilityWidgetPage = () => {
  const { tenantID, productName } = useParams()
  const productInfo = {
    productName: productName || 'Default Product',
    productImage: 'default-image-url',
  }

  return (
    <>
      <Metadata title="Product Detail" description="Product Detail page" />
      <Layout>
        <Header style={{ background: '#fff', padding: '0 50px' }}>
          <div style={{ float: 'left', fontSize: '24px', fontWeight: 'bold' }}>
            EcoEssence Living
          </div>
          <Menu mode="horizontal" style={{ float: 'right' }}>
            <Menu.Item key="shop">Shop</Menu.Item>
            <Menu.Item key="refills">Get Refills</Menu.Item>
            <Menu.Item key="how">How It Works</Menu.Item>
            <Menu.Item key="mission">Mission</Menu.Item>
            <Menu.Item key="account" icon={<UserOutlined />}>
              Account
            </Menu.Item>
            <Menu.Item key="search" icon={<SearchOutlined />} />
            <Menu.Item key="cart" icon={<ShoppingCartOutlined />} />
          </Menu>
        </Header>
        <Content style={{ padding: '50px 50px' }}>
          <Space size={40} align="start">
            <Card
              cover={<img alt="Product" src="/images/dishwash_product.jpg" />}
              style={{ width: 400 }}
            />
            <div style={{ flex: 1 }}>
              <Title level={2}>Eco-Friendly Cleaning Kit</Title>
              <Space>
                <Text type="success">New Scent!</Text>
                <Text type="warning">10% Kit Savings</Text>
              </Space>
              <Tabs defaultActiveKey="1">
                <TabPane tab="Overview" key="1">
                  <Paragraph>
                    Save space and money while also doing good for the planet
                    with this duo of two of our sustainable best-sellers. Our
                    Laundry Detergent Tablets and Dishwasher Detergent Tablets
                    have all of the cleaning power without any of the plastic.
                    100% plastic-free and made with ingredients safe for you and
                    the planet.
                  </Paragraph>
                  <List
                    itemLayout="horizontal"
                    dataSource={[
                      'Clean Ingredients: Made with plant-based and planet-friendly ingredients',
                      'Effective: Independently tested to perform alongside major brands',
                      'Space Saving: Ditch the bulk with compact Forever Tins and refill tablets that take up a fraction of the space of the equivalent plastic bottle',
                      '10% Bundle Savings: Get both best-sellers in one kit and save on costs and emissions',
                    ]}
                    renderItem={(item) => (
                      <List.Item>
                        <Text>• {item}</Text>
                      </List.Item>
                    )}
                  />
                </TabPane>
                <TabPane tab="Ingredients" key="2">
                    <Paragraph>
                    Our products are made with carefully selected, eco-friendly ingredients:
                    </Paragraph>
                    <List
                    itemLayout="horizontal"
                    dataSource={[
                      'White Vinegar',
                      'Baking Soda',
                      'Lemon Juice',
                      'Castile Soap',
                      'Essential Oils',
                      'Water',
                    ]}
                    renderItem={(item) => (
                      <List.Item>
                      <Text>• {item}</Text>
                      </List.Item>
                    )}
                    />
                </TabPane>
                <TabPane tab="Sustainability" key="3">
                  <ProductSustainabilityWidget />
                </TabPane>
              </Tabs>
              <Button
                type="primary"
                icon={<ShoppingCartOutlined />}
                size="large"
                style={{ marginTop: 20 }}
              >
                Add to Cart
              </Button>
            </div>
          </Space>
        </Content>
      </Layout>
    </>
  )
}

export default ProductSustainabilityWidgetPage
