// Pass props to your component by passing an `args` object to your story
//
// ```tsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import IntermediateExchanges from './IntermediateExchanges'

const meta: Meta<typeof IntermediateExchanges> = {
  component: IntermediateExchanges,
  tags: ['autodocs'],
}

export default meta

type Story = StoryObj<typeof IntermediateExchanges>

export const Primary: Story = {}
