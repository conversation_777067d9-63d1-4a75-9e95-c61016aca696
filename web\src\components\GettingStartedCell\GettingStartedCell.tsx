import React, { useRef, useState } from 'react'
import type { DashboardMetricsQuery } from 'types/graphql'
import type { CellSuccessProps, CellFailureProps } from '@redwoodjs/web'
import {
  Card,
  Button,
  List,
  Progress,
  Avatar,
  Col,
  Row,
  Tag,
  Spin,
  Skeleton,
  Tooltip,
  Tour,
  Checkbox,
  Typography,
  Divider,
  Space,
} from 'antd'
const { Title, Text } = Typography
import {
  CommentOutlined,
  AlignLeftOutlined,
  PlaySquareOutlined,
  CustomerServiceOutlined,
  DotChartOutlined,
} from '@ant-design/icons'
import { Link, navigate, routes } from '@redwoodjs/router'
import { useAuth, getOrgMemberInfo } from 'src/auth'
import GettingStartedTour from '../GettingStartedTour/GettingStartedTour'
import ErrorHandler from '../ErrorHandler/ErrorHandler'
import './style.css'
import UpgradePlan from '../UpgradePlan/UpgradePlan'
import { renderImpactFactorUnit } from 'src/utils/helper'

export const QUERY = gql`
  query DashboardMetricsQuery {
    getDashboardMetrics {
      totalProductEmissions
      averageProductFootprint
      totalProducts
      productCategories
      lifeCycleEmissionsByProductcategory
      lifeCycleEmissions {
        rawMaterialsEmissions
        manufacturingEmissions
        distributionEmissions
        consumerUseEmissions
        eolEmissions
      }
      highestCarbonFootprintProducts {
        productName
        productId
        brand
        category
        imageUrl
        totalEmissions
      }
      lowestCarbonFootprintProducts {
        productName
        productId
        brand
        category
        imageUrl
        totalEmissions
      }
      productsByRegion
    }
  }
`

export const Loading = () => (
  <div>
    <Spin size="large" tip="Loading...">
      <Skeleton paragraph={{ rows: 30 }} active />
    </Spin>
  </div>
)

const knowledgeBaseLink =
  'https://rounded-lyre-c7b.notion.site/023c6b325eb449428bccd895bd2c7fdb?v=cb5ed850a5c4403b8efab027ce1584dc'
const gettingStartedKnowledgeBaseLink =
  'https://rounded-lyre-c7b.notion.site/Getting-Started-Your-First-Product-LCA-579ed302f09e47318ab8f551bb33cac4'
const lcaKnowledgeBaseLink =
  'https://rounded-lyre-c7b.notion.site/What-is-a-Life-Cycle-Assessment-LCA-1d3bbe71f4ba4c9198096fa965f7b382'
const lcaDataKnowledgeBaseLink =
  'https://rounded-lyre-c7b.notion.site/Data-Requirements-for-an-LCA-7118598d5f324617bcb611e396e94fdd'

export const Empty = () => <div>Empty</div>

export const Failure = ({ error }: CellFailureProps) => (
  <ErrorHandler error={error} />
)

export const Success = ({
  getDashboardMetrics
}: CellSuccessProps<DashboardMetricsQuery>) => {
  if (!getDashboardMetrics) {
    return <ErrorHandler />
  }

  const dashboardMetrics = getDashboardMetrics

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const isTourCompleted =
    userMetadata.user.metadata?.milestone_productTour ?? false

  const [isTourOpen, setIsTourOpen] = useState(
    !(userMetadata.user.metadata?.milestone_productTour ?? false)
  )

  const [upgradePlanModalIsVisible, setUpgradePlanModalIsVisible] =
    useState(false)

  const showUpgradePlanModal = () => {
    setUpgradePlanModalIsVisible(true)
  }

  const tenantID =
    orgMemberInfo && orgMemberInfo.urlSafeOrgName
      ? orgMemberInfo.urlSafeOrgName
      : null

  const tasks = [
    {
      title: 'Complete Product Tour',
      status: isTourCompleted ? 'Completed' : 'To Do',
    },
    {
      title: 'Create My First Product',
      status: userMetadata.user.metadata?.milestone_createProduct
        ? 'Completed'
        : 'To Do',
    },
    {
      title: 'Download Product Impact Report',
      status: userMetadata.user.metadata?.milestone_downloadReport
        ? 'Completed'
        : 'To Do',
    },
    {
      title: 'Upgrade To Full Version',
      status: !userMetadata.user.metadata?.isTrialUser ? 'Completed' : 'To Do',
    },
  ]

  const helpCenterItems = [
    {
      icon: <AlignLeftOutlined />,
      title: 'Take a Product Tour',
      duration: '8 min',
    },
    {
      icon: <DotChartOutlined />,
      title: 'Try out our Labs',
      duration: '10 min',
    },
    {
      icon: <PlaySquareOutlined />,
      title: 'Getting started - tutorials',
      duration: '~5 min each',
    },
    {
      icon: <AlignLeftOutlined />,
      title: 'LCA methods and databases',
      duration: '6 min',
    },
    {
      icon: <PlaySquareOutlined />,
      title: 'The next steps - tutorials',
      duration: '~5 min each',
    },
    {
      icon: <PlaySquareOutlined />,
      title: 'LCA fundamentals',
      duration: '~3 min each',
    },
  ]

  const resourceItems = [
    { icon: <AlignLeftOutlined />, title: 'Knowledge Base' },
    { icon: <CustomerServiceOutlined />, title: 'Contact Support' },
    { icon: <CommentOutlined />, title: 'Feedback' },
  ]

  const recentlyViewedProducts = JSON.parse(
    localStorage.getItem('recentlyViewedProduct') || '[]'
  )

  const clickWithDelay = (element) => {
    return new Promise((resolve) => {
      if (element) {
        element.click()
        setTimeout(resolve, 500) // 500ms delay
      } else {
        resolve()
      }
    })
  }

  const handleListItemClick = (item) => {
    if (item.title == 'Take a Product Tour') {
      setIsTourOpen(true)
      return
    }

    if (item.title == 'Try out our Labs') {
      navigate(routes.labHome())
      return
    }

    if (item.title == 'Complete Product Tour') {
      if (!isTourCompleted) {
        setIsTourOpen(true)
      }
      return
    }

    if (item.title == 'Create My First Product' && item.status == 'To Do') {
      navigate(routes.addProduct())
      return
    }

    if (item.title == 'Upgrade To Full Version' && item.status == 'To Do') {
      showUpgradePlanModal()
      return
    }

    if (item.title == 'Getting started - tutorials') {
      window.open(gettingStartedKnowledgeBaseLink, '_blank')
      return
    }

    if (item.title == 'LCA methods and databases') {
      window.open(lcaDataKnowledgeBaseLink, '_blank')
      return
    }

    if (item.title == 'The next steps - tutorials') {
      window.open(gettingStartedKnowledgeBaseLink, '_blank')
      return
    }

    if (item.title == 'LCA fundamentals') {
      window.open(lcaKnowledgeBaseLink, '_blank')
      return
    }

    if (item.title == 'Knowledge Base') {
      window.open(knowledgeBaseLink, '_blank')
      return
    }

    if (item.title == 'Contact Support') {
      const helpMenu = document.querySelector(`.help-and-support-menu`)
      helpMenu?.click()
      setTimeout(() => {
        const contactSupport = document.querySelector(`#contact-support-btn`)
        contactSupport?.click()
      }, 0.1)
    }

    if (item.title == 'Feedback') {
      const helpMenu = document.querySelector(`.help-and-support-menu`)
      helpMenu?.click()
      setTimeout(() => {
        const feedback = document.querySelector(`#feedback-btn`)
        feedback?.click()
      }, 0.1)
    }
  }
  return (
    <>
      <GettingStartedTour
        isOpen={isTourOpen}
        onClose={() => setIsTourOpen(false)}
      />
      <UpgradePlan
        visible={upgradePlanModalIsVisible}
        onClose={() => setUpgradePlanModalIsVisible(false)}
        message="Upgrade to the full version to unlock all features"
      />
      <Row gutter={[16, 16]}>
        <Col span={16}>
          <Card title={<span>Tasks</span>}>
            <List
              dataSource={tasks}
              renderItem={(item) => (
                <List.Item
                  style={{ marginTop: 15, cursor: 'pointer' }}
                  onClick={() => handleListItemClick(item)}
                  actions={[
                    <Text
                      type={
                        item.status == 'Completed' ? 'secondary' : 'primary'
                      }
                    >
                      {item.status}
                    </Text>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Checkbox
                        className="task-checkbox"
                        checked={item.status === 'Completed'}
                        onClick={(e) => e.stopPropagation()}
                      />
                    }
                    title={item.title}
                  />
                </List.Item>
              )}
            />
            <Divider />
            <Button
              style={{
                display: userMetadata.user.metadata?.isTrialUser
                  ? 'inline-block'
                  : 'none',
              }}
              id="upgrade-now-btn"
              onClick={showUpgradePlanModal}
              type="link"
              block
            >
              Upgrade Now &rarr;
            </Button>
          </Card>
        </Col>
        <Col span={8}>
          <Card title={<span>Help Center</span>}>
            <List
              dataSource={helpCenterItems}
              renderItem={(item) => (
                <List.Item
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleListItemClick(item)}
                >
                  <List.Item.Meta
                    avatar={<p style={{ fontSize: 18 }}>{item.icon}</p>}
                    title={item.title}
                    description={
                      <p style={{ fontSize: 12 }}>{item.duration}</p>
                    }
                  />
                </List.Item>
              )}
            />
            <Button type="link" target="_blank" href={knowledgeBaseLink} block>
              &#8599; Learn More In our Help Center
            </Button>
          </Card>
        </Col>
      </Row>
      <Row style={{ marginTop: 10 }} gutter={[16, 16]}>
        <Col span={16}>
          <Card title={<span>Recent Products</span>}>
            <Space size="large">
              <Card
                onClick={() => {
                  navigate(routes.addProduct())
                }}
                style={{
                  width: 240,
                  height: 120,
                  border: '1px dashed #d9d9d9',
                }}
                hoverable
              >
                <Text
                  style={{ marginLeft: '50%', fontSize: 22 }}
                  type="secondary"
                >
                  &#43;
                </Text>
                <br />
                <Text style={{ marginLeft: '25%' }} type="secondary">
                  Add a Product
                </Text>
              </Card>
              {recentlyViewedProducts.map((product, index) => (
                <Card
                  onClick={() => {
                    navigate(
                      routes.productDetail({
                        tenantID: tenantID,
                        productId: encodeURIComponent(product.productId),
                      })
                    )
                  }}
                  key={index}
                  style={{ width: 240, height: 120 }}
                  hoverable
                >
                  <Avatar
                    style={{ marginTop: -25 }}
                    shape="square"
                    size={30}
                    src={product.productImage}
                  />
                  <br />
                  <Text>{product.productName}</Text>
                  <br />
                  <Text style={{ fontSize: 12 }} type="secondary">
                    {product?.emissions?.toFixed(2)} {renderImpactFactorUnit(userMetadata)}
                  </Text>
                </Card>
              ))}
            </Space>
            <Button
              style={{ marginTop: 6 }}
              id="upgrade-now-btn"
              href="/products"
              type="link"
              block
            >
              View All Products &rarr;
            </Button>
          </Card>
        </Col>
        <Col span={8}>
          <Card title={<span>Resources</span>}>
            <List
              dataSource={resourceItems}
              renderItem={(item) => (
                <List.Item
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleListItemClick(item)}
                >
                  <List.Item.Meta
                    avatar={<p style={{ fontSize: 18 }}>{item.icon}</p>}
                    title={item.title}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </>
  )
}
