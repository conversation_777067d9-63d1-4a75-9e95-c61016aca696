export const schema = gql`

  type CreatedSupplierResponse {
    id: Int!
    supplierName: String!
    city: String
    country: String!
    latitude: Float
    longitude: Float
  }

  input CreateSupplierInput {
    supplier_name: String!
    supplier_type: String!
    supplier_level: Int!
    city: String
    country: String!
    latitude: Float
    longitude: Float
  }

  type Mutation {
    createSupplier(
      supplier: CreateSupplierInput!
    ): CreatedSupplierResponse @requireAuth
  }
`
