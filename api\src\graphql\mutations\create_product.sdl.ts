export const schema = gql`
  type Product {
    productId: String!
    productName: String!
    brand: String
    countryOfUse: String
    functionalUnit: String
    primaryCategory: String
    annualSalesVolumeUnits: Int
  }

  input CreateProductInput {
    product_type: String!
    product_id: String!
    product_name: String!
    brand: String
    country_of_use: String
    factory_city: String
    factory_country: String
    functional_unit: String
    primary_category: String
    product_image: String
    annual_sales_volume_units: Int
    tags: [String]
    old_product_id: String
  }

  type Mutation {
    createProduct(product: CreateProductInput!): Product @requireAuth
  }
`
