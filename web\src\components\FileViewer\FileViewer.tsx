'use client'

import { Spin, message } from 'antd';
import { useEffect, useState } from 'react'
import { pdfjs, Document, Page } from 'react-pdf'
import mammoth from 'mammoth'
import jsPDF from 'jspdf'
import 'jspdf-autotable'
import html2pdf from 'html2pdf.js';
import <PERSON> from 'papaparse'
import * as XLSX from 'xlsx';
import epubjs from 'epubjs';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css'
import 'react-pdf/dist/esm/Page/TextLayer.css'
import './style.css'

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/legacy/build/pdf.worker.min.mjs',
  import.meta.url
).toString()

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

const options = {
  cMapUrl: '/cmaps/',
  standardFontDataUrl: '/standard_fonts/',
}

const maxWidth = 350

const fileTypes = {
  isExcel: (contentType) => [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ].includes(contentType),
  isCsvOrTsv: (contentType) => [
    'text/csv',
    'text/tab-separated-values',
  ].includes(contentType),
  isDocx: (contentType) =>
    contentType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  isOdt: (contentType) =>
    contentType === 'application/vnd.oasis.opendocument.text',
}

function decodeBase64(data: string): Uint8Array {
  const base64 = data.split(',')[1]
  const raw = window.atob(base64);
  const rawLength = raw.length;
  const array = new Uint8Array(rawLength);

  for (let i = 0; i < rawLength; i++) {
    array[i] = raw.charCodeAt(i);
  }

  return array;
};

function bytesToString(array: Uint8Array): string {
  return new TextDecoder().decode(array)
}

function decodeBase64ToString(data: string): string {
  return bytesToString(decodeBase64(data))
}

function decodeBase64ToBuffer(data: string): ArrayBuffer {
  return decodeBase64(data).buffer
}

async function generatePdfFromHtml(html: string): Promise<Blob | null> {
  const pdfBlob = await html2pdf()
    .from(html)
    .set({
      margin: 1,
      filename: 'converted.pdf',
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 2, logging: true, dpi: 192, letterRendering: true },
      jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' },
    })
    .outputPdf('blob');

  return pdfBlob;
};

async function convertToPdf(data, contentType) {
  if (contentType === 'application/pdf') {
    return data
  }

  const doc = new jsPDF()

  if (fileTypes.isCsvOrTsv(contentType)) {
    const text = decodeBase64ToString(data)
    const parsedData = Papa.parse(text, {
      delimiter: contentType === 'text/csv' ? ',' : '\t',
    }).data

    doc.autoTable({
      head: [parsedData[0]],
      body: parsedData.slice(1),
    })

    return doc.output('blob')
  } else if (fileTypes.isDocx(contentType) || fileTypes.isOdt(contentType)) {
    const arrayBuffer = decodeBase64ToBuffer(data);
    const html = await mammoth.convertToHtml({ arrayBuffer });

    return await generatePdfFromHtml(html.value);
  } else if (fileTypes.isExcel(contentType)) {
    const arrayBuffer = decodeBase64ToBuffer(data);
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });

    workbook.SheetNames.forEach((sheetName, index) => {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (index > 0) {
        doc.addPage();
      }

      doc.text(sheetName, 14, 16);
      doc.autoTable({
        head: [jsonData[0]],
        body: jsonData.slice(1),
        startY: 20,
      });
    });

    return doc.output('blob')
  } else {
    throw new Error('Unsupported file format')
  }
}

const FileViewer = ({ fileData = null, contentType = null }) => {
  const [file, setFile] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [numPages, setNumPages] = useState()
  const [containerRef, setContainerRef] = useState(null)
  const [containerWidth, setContainerWidth] = useState()

  useEffect(() => {
    if (fileData) {
      convertToPdf(fileData, contentType).then((pdf) => {
        setIsLoading(false)
        setFile(pdf)
      })
      .catch((error) => {
        console.error('Error converting file:', error);
        message.error(`Unable to show file preview for content type ${contentType}.`);
        setIsLoading(false);
      });
    }
  }, [fileData, contentType])

  function onDocumentLoadSuccess({ numPages: nextNumPages }) {
    setNumPages(nextNumPages)
  }

  return (
    <div className="FileViewer">
      <div className="FileViewer__container">
        <div className="FileViewer__container__document" ref={setContainerRef}>
          {
            file ? (
              <Document
                file={file}
                onLoadSuccess={onDocumentLoadSuccess}
                options={options}
              >
                {Array.from(new Array(numPages), (el, index) => (
                  <Page
                    key={`page_${index + 1}`}
                    pageNumber={index + 1}
                    width={
                      containerWidth ? Math.min(containerWidth, maxWidth) : maxWidth
                    }
                  />
                ))}
              </Document>
            ) : null
          }
          {
            isLoading ? (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <Spin />
              </div>
            ) : null
          }
        </div>
      </div>
    </div>
  )
}

export default FileViewer
