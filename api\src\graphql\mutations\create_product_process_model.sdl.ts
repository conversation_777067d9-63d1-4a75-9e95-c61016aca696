export const schema = gql`
  input ProcessModelLocationInput {
    city: String
    country: String
  }

  input ProcessModelEdgeInput {
    from_node_id: Int!
    to_node_id: Int!
  }

  input ProcessModelEmissionsFactorInput {
    activity_name: String
    reference_product_name: String
    geography: String
    source: String
  }

  input ProcessModelNodeInput {
    id: Int!
    name: String!
    component_name: String
    description: String
    packaging_level: String
    recycled_content_rate: Float
    node_type: String!
    quantity: Float
    amount: Float
    unit: String
    location: ProcessModelLocationInput
    emissions_factor: ProcessModelEmissionsFactorInput
    supplier_id: Int
    scrap_rate: Float
    scrap_fate: String
    mass_allocation_per_kg: Boolean
    eol_recycling_rate: Float
    eol_landfill_rate: Float
    eol_incineration_rate: Float
    eol_composting_rate: Float
  }

  input CreateProcessModelInput {
    nodes: [ProcessModelNodeInput]!
    edges: [ProcessModelEdgeInput]!
  }

  type Mutation {
    createProductProcessModel(
      productId: String!
      processModel: CreateProcessModelInput!
    ): String @requireAuth
  }
`
