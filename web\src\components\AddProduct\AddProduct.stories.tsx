// Pass props to your component by passing an `args` object to your story
//
// ```jsx
// export const Primary: Story = {
//  args: {
//    propName: propValue
//  }
// }
// ```
//
// See https://storybook.js.org/docs/react/writing-stories/args.

import type { Meta, StoryObj } from '@storybook/react'

import AddProduct from './AddProduct'

const meta: Meta<typeof AddProduct> = {
  component: AddProduct,
}

export default meta

type Story = StoryObj<typeof AddProduct>

export const Primary: Story = {}
