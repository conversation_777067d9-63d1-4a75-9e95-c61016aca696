import React, { useState } from 'react'
import { ReadOutlined } from '@ant-design/icons'

import { Card, Skeleton } from 'antd'

const { Meta } = Card

interface CardProps {
  title: string
  description: string
  loading: boolean
}

const CardComponent: React.FC<CardProps> = ({
  title,
  description,
  loading,
}) => {
  const [isHovered, setIsHovered] = useState(false)

  const handleCardMouseEnter = () => {
    setIsHovered(true)
  }

  const handleCardMouseLeave = () => {
    setIsHovered(false)
  }

  return (
    <Card
      style={{
        width: 200,
        height: 125,
        margin: '15px 15px 15px 15px',
        transition: 'transform 0.3s',
        transform: isHovered ? 'scale(1.1)' : 'scale(1)',
      }}
      loading={loading}
      onMouseEnter={handleCardMouseEnter}
      onMouseLeave={handleCardMouseLeave}
    >
      <Skeleton loading={loading} avatar>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <div style={{ marginBottom: '5px' }}>
            <ReadOutlined style={{ fontSize: '24px' }} />
          </div>
          <Meta
            title={
              <h4
                style={{
                  fontSize: '14px',
                  textAlign: 'center',
                  whiteSpace: 'normal',
                }}
              >
                {title}
              </h4>
            }
            description={
              <p
                style={{
                  fontSize: '12px',
                  textAlign: 'center',
                  whiteSpace: 'normal',
                }}
              >
                {description}
              </p>
            }
          />
        </div>
      </Skeleton>
    </Card>
  )
}

export default CardComponent
