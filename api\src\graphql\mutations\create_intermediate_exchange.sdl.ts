export const schema = gql`
  type CreatedIntermediateExchange {
    id: Int!
    exchangeName: String!
    amount: Float!
    unit: String!
  }

  input IntermediateExchangeEmissionsFactorInput {
    activity_name: String!
    activity_type: String!
    reference_product_name: String!
    geography: String!
    source: String!
    unit: String!
  }

  input IntermediateExchangeInput {
    exchange_name: String!
    amount: Float!
    unit: String!
    input_stream: Boolean!
    exchange_emissions_factor: IntermediateExchangeEmissionsFactorInput!
  }

  input CreateIntermediateExchangeInput {
    parent_emissions_factor: IntermediateExchangeEmissionsFactorInput!
    exchange: IntermediateExchangeInput!
  }

  type Mutation {
    createIntermediateExchange(
      intermediateExchange: CreateIntermediateExchangeInput!
    ): CreatedIntermediateExchange! @requireAuth
  }
`
