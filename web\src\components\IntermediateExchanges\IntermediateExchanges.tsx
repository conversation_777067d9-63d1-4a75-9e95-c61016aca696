import { useState, useEffect } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import {
  Button,
  Drawer,
  Form,
  Input,
  InputNumber,
  Modal,
  Space,
  message,
  Steps,
  Spin,
  Descriptions,
  Tag,
  Tabs,
  Tooltip,
  Row,
  Col,
  Radio,
  Divider,
  Card
} from 'antd'
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  SearchOutlined,
  SaveOutlined
} from '@ant-design/icons'
import { v4 as uuidv4 } from 'uuid'
import DataTable from '../DataTable/DataTable'
import {
  CREATE_INTERMEDIATE_EXHANGE_MUTATION,
  UPDATE_INTERMEDIATE_EXHANGE_MUTATION,
  UPDATE_EMISSIONS_FACTOR_MUTATION,
  DELETE_INTERMEDIATE_EXHANGE_MUTATION,
  EF_INTERMEDIATE_EXCHANGES_QUERY,
  SEARCH_EMISSIONS_FACTORS_QUERY,
} from 'src/utils/graphql'
const { confirm } = Modal

const IntermediateExchanges = ({
  parentActivity,
  onClose,
  isOpen,
  onUpdate,
  editMode = false,
  orgName = null,
}) => {
  // State declarations
  const [loading, setLoading] = useState(false)
  const [_parentActivity, setParentActivity] = useState(parentActivity)
  const [intermediateExchanges, setIntermediateExchanges] = useState([])
  const [emissionsFactorsSearchResults, setEmissionsFactorsSearchResults] =
    useState([])
  const [intermediateExchangesTableKey, setIntermediateExchangesTableKey] =
    useState(uuidv4())
  const [
    emissionsFactorsSearchResultsTableKey,
    setEmissionsFactorsSearchResultsTableKey,
  ] = useState(uuidv4())
  const [currentAddExchangeStep, setCurrentAddExchangeStep] = useState(0)
  const [emissionsFactorSearchValue, setEmissionsFactorSearchValue] =
    useState(null)
  const [isAddExchangeModalOpen, setIsAddExchangeModalOpen] = useState(false)
  const [isEmissionsFactorSelectorOpen, setIsEmissionsFactorSelectorOpen] =
    useState(false)
  const [selectedExchangeActivity, setSelectedExchangeActivity] = useState({
    activityName: null,
    referenceProduct: null,
    geography: null,
    source: null,
    unit: null,
  })
  const [editingExchange, setEditingExchange] = useState(null)
  const [sharedScope, setIsSharedScope] = useState(true)
  const [addExchangeForm] = Form.useForm()
  const [isEditActivityDrawerOpen, setIsEditActivityDrawerOpen] = useState(false)
  const [editActivityForm] = Form.useForm()

  // GraphQL hooks
  const [createIntermediateExchange, { loading: createExchangeIsLoading }] =
    useMutation(CREATE_INTERMEDIATE_EXHANGE_MUTATION)
  const [updateIntermediateExchange, { loading: udpateExchangeIsLoading }] =
    useMutation(UPDATE_INTERMEDIATE_EXHANGE_MUTATION)
  const [deleteIntermediateExchange, { loading: deleteExchangeIsLoading }] =
    useMutation(DELETE_INTERMEDIATE_EXHANGE_MUTATION)
  const [getEFIntermediateExchanges, { loading: queryLoading }] = useLazyQuery(
    EF_INTERMEDIATE_EXCHANGES_QUERY
  )
  const [updateEmissionsFactor, { loading: updateEFIsLoading }] =
    useMutation(UPDATE_EMISSIONS_FACTOR_MUTATION)

  const [searchEmissionsFactors, { loading: searchEmissionsFactorsIsLoading }] =
    useLazyQuery(SEARCH_EMISSIONS_FACTORS_QUERY)

  useEffect(() => {
    if (isOpen && _parentActivity) {
      fetchIntermediateExchanges()
    }
  }, [isOpen, _parentActivity])

  useEffect(() => {
    if (parentActivity) {
      setParentActivity(parentActivity)
    }
  }, [parentActivity])

  useEffect(() => {
    if (isEditActivityDrawerOpen && _parentActivity) {
      editActivityForm.setFieldsValue({
        activityName: _parentActivity.activityName,
        referenceProduct: _parentActivity.referenceProduct,
        description: _parentActivity.description,
      })
    }
  }, [isEditActivityDrawerOpen, _parentActivity, editActivityForm])

  const handleEmissionsFactorSearch = async () => {
    if (!emissionsFactorSearchValue?.length) {
      message.error('Please enter a search term')
      return
    }

    const response = await searchEmissionsFactors({
      variables: {
        activityName: emissionsFactorSearchValue,
      },
    })

    if (!response?.data?.searchEmissionsFactors) {
      message.error('No results found')
      return
    }

    const searchResults = response.data.searchEmissionsFactors.map(
      (emissionsFactor) => {
        return {
          key: uuidv4(),
          activityName: emissionsFactor.activityName,
          geography: emissionsFactor.geography,
          source: emissionsFactor.source,
          referenceProduct: emissionsFactor.referenceProduct,
          unit: emissionsFactor.unit,
        }
      }
    )

    setEmissionsFactorsSearchResults(searchResults)
    setEmissionsFactorsSearchResultsTableKey(uuidv4())
  }

  const fetchIntermediateExchanges = async () => {
    if (!_parentActivity) return
    setLoading(true)

    try {
    setIsSharedScope(true)
    let response = await getEFIntermediateExchanges({
      variables: {
        activityName: _parentActivity.activityName,
        referenceProduct: _parentActivity.referenceProduct,
        geography: _parentActivity.geography,
        source: _parentActivity.source,
        sharedScope: false,
      },
    })

    if (_parentActivity.source === orgName?.toUpperCase()) {
      setIsSharedScope(false)
      setIntermediateExchanges(response?.data?.getEFIntermediateExchanges ?? [])
      setIntermediateExchangesTableKey(uuidv4())
      return
    }

    if (_parentActivity.exchanges?.length) {
      setIntermediateExchanges(_parentActivity.exchanges)
      setIntermediateExchangesTableKey(uuidv4())
      return
    }

    if (!response?.data?.getEFIntermediateExchanges?.length) {
      response = await getEFIntermediateExchanges({
        variables: {
          activityName: _parentActivity.activityName,
          referenceProduct: _parentActivity.referenceProduct,
          geography: _parentActivity.geography,
          source: _parentActivity.source,
          sharedScope: true,
        },
      })

      setIntermediateExchanges(response?.data?.getEFIntermediateExchanges ?? [])
      setIntermediateExchangesTableKey(uuidv4())
    }

    } catch (error) {
      setIntermediateExchanges([])
      setIntermediateExchangesTableKey(uuidv4())
      console.error('Failed to fetch exchanges:', error)
      message.error('Failed to fetch exchanges')
    } finally {
      setLoading(false)
    }
  }

  const handleCloseModal = () => {
    setIsAddExchangeModalOpen(false)
    setCurrentAddExchangeStep(0)
    setEditingExchange(null)
    setSelectedExchangeActivity(null)
    addExchangeForm.resetFields()
  }

  const handleCreateOrUpdateExchange = async () => {
    try {
      if (!selectedExchangeActivity) {
        return message.error('Please select an emissions factor')
      }

      const exchangeData = {
        exchange_name: addExchangeForm.getFieldValue('exchangeName'),
        amount: addExchangeForm.getFieldValue('amount'),
        unit: addExchangeForm.getFieldValue('unit'),
        input_stream:
          parseFloat(addExchangeForm.getFieldValue('amount')) > 0
            ? true
            : false,
        exchange_emissions_factor: {
          activity_name: selectedExchangeActivity.activityName,
          activity_type: 'Raw Materials',
          reference_product_name: selectedExchangeActivity.referenceProduct,
          geography: selectedExchangeActivity.geography,
          source: selectedExchangeActivity.source,
          unit: selectedExchangeActivity.unit,
        },
      }

      if (editingExchange) {
        await updateIntermediateExchange({
          variables: {
            exchangeId: editingExchange.id,
            intermediateExchange: exchangeData,
          },
        })
      } else {
        await createIntermediateExchange({
          variables: {
            intermediateExchange: {
              parent_emissions_factor: {
                activity_name: _parentActivity.activityName,
                activity_type: 'Raw Materials',
                reference_product_name: _parentActivity.referenceProduct,
                geography: _parentActivity.geography,
                source: _parentActivity.source,
                unit: _parentActivity.unit,
              },
              exchange: exchangeData,
            },
          },
        })
      }

      message.success(
        `Exchange ${editingExchange ? 'updated' : 'created'} successfully`
      )
      handleCloseModal()
      await fetchIntermediateExchanges()
      if (onUpdate) onUpdate(_parentActivity)
    } catch (error) {
      console.error('Failed to save exchange:', error)
      message.error('Failed to save exchange')
    }
  }

  const handleDeleteExchange = async (record) => {
    confirm({
      title: 'Do you want to delete this intermediate exchange?',
      icon: <DeleteOutlined />,
      onOk: async () => {
        try {
          await deleteIntermediateExchange({
            variables: {
              exchangeId: record.id,
            },
          })

          message.success('Exchange deleted successfully')
          await fetchIntermediateExchanges()
          if (onUpdate) onUpdate(_parentActivity)
          return true
        } catch (error) {
          message.error('Failed to delete exchange')
          return false
        }
      },
      onCancel() {
        return false
      },
    })
  }

  const handleEditActivity = async () => {
    editActivityForm.validateFields().then(async (values) => {
      const updateEfInput = {
        activity_name: values.activityName,
        reference_product_name: values.referenceProduct,
        activity_description: values.description,
      }

      setLoading(true)

      try {
        const updateEFResponse = await updateEmissionsFactor({
          variables: {
            emissionsFactorId: _parentActivity.efId,
            emissionsFactor: updateEfInput,
          },
        })
        setParentActivity({
          ..._parentActivity,
          activityName: updateEFResponse.data.updateEmissionsFactor.activityName,
          referenceProduct: updateEFResponse.data.updateEmissionsFactor.referenceProduct,
          description: updateEFResponse.data.updateEmissionsFactor.description,
        })
        message.success('Activity updated successfully')
        setIsEditActivityDrawerOpen(false)
        setLoading(false)
      } catch (error) {
        message.error('Failed to update activity')
        console.log(error)
        setLoading(false)
      }

    })
  }

  const columns = [
    {
      title: 'Activity',
      dataIndex: 'exchangeName',
      width: '30%',
    },
    {
      title: 'Emissions Factor',
      width: '40%',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <div>{record.exchangeEmissionsFactor?.activityName}</div>
          <Space>
            <Tag color="blue">{record.exchangeEmissionsFactor?.source}</Tag>
            <Tag>{record.exchangeEmissionsFactor?.geography}</Tag>
          </Space>
        </Space>
      ),
    },
    {
      title: 'Amount',
      width: '15%',
      render: (_, record) => <span>{`${record.amount} ${record.unit}`}</span>,
    },
  ]

  if (editMode && !sharedScope) {
    columns.push({
      title: 'Actions',
      width: '15%',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingExchange(record)
              setSelectedExchangeActivity(record.exchangeEmissionsFactor)
              addExchangeForm.setFieldsValue({
                id: record.id,
                exchangeName: record.exchangeName,
                amount: record.amount,
                unit: record.unit,
              })
              setEmissionsFactorsSearchResults([
                {
                  id: uuidv4(),
                  activityName: record.exchangeEmissionsFactor.activityName,
                  referenceProduct:
                    record.exchangeEmissionsFactor.referenceProduct,
                  geography: record.exchangeEmissionsFactor.geography,
                  source: record.exchangeEmissionsFactor.source,
                  unit: record.exchangeEmissionsFactor.unit,
                },
              ])
              setEmissionsFactorsSearchResultsTableKey(uuidv4())
              setIsAddExchangeModalOpen(true)
            }}
          />
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteExchange(record)}
          />
        </Space>
      ),
    })
  }

  const validateAddExchangeStep = async (step) => {
    switch (step) {
      case 0:
        if (!selectedExchangeActivity.activityName) {
          message.error('Please select an activity')
          return false
        }
        return true
      case 1:
        try {
          await addExchangeForm.validateFields().then((values) => {
            setCurrentAddExchangeStep(currentAddExchangeStep + 1)
            return true
          })
        } catch (error) {
          message.error('Please fill in all required fields')
          return false
        }
      default:
        return true
    }
  }

  const addExchangeStepNext = async () => {
    const canProceed = await validateAddExchangeStep(currentAddExchangeStep)
    if (canProceed) {
      setCurrentAddExchangeStep(currentAddExchangeStep + 1)
    }
  }

  const handleAddExchangeStepChange = async (step) => {
    if (step < currentAddExchangeStep) {
      // Moving backwards, allow without validation
      setCurrentAddExchangeStep(step)
    } else if (step > currentAddExchangeStep) {
      // Moving forwards, validate each step
      for (let i = currentAddExchangeStep; i < step; i++) {
        const canProceed = await validateAddExchangeStep(i)
        if (!canProceed) {
          return // Stop if validation fails
        }
      }
      setCurrentAddExchangeStep(step)
    }
  }

  const IntermediateExchangeEmissionsFactorTableColumns = [
    {
      title: 'Select',
      render: (key, record) => (
        <Radio
          value={`${record['activityName']}/${record['referenceProduct']}/${record['geography']}/${record['source']}`}
          checked={
            selectedExchangeActivity?.activityName == record['activityName'] &&
            selectedExchangeActivity?.referenceProduct ==
              record['referenceProduct'] &&
            selectedExchangeActivity?.geography == record['geography'] &&
            selectedExchangeActivity?.source == record['source']
          }
          onChange={() => {
            addExchangeForm.setFieldsValue({
              unit: record['unit'],
            })
            setSelectedExchangeActivity({
              activityName: record['activityName'],
              referenceProduct: record['referenceProduct'],
              geography: record['geography'],
              source: record['source'],
              unit: record['unit'],
            })
          }}
        />
      ),
    },
    {
      title: 'Activity',
      render: (key, record) => (
        <>
          <p>
            {record.activityName} - {record.geography}
          </p>
          <Tag color="orange">{record.referenceProduct}</Tag>
          <Tag color="green">{record.source}</Tag>
        </>
      ),
    },
  ]

  const addExchangeSteps = [
    {
      title: (
        <>
          <p style={{ width: 120, paddingTop: 8 }}>Emissions Factor</p>
        </>
      ),
      content: (
        <>
          <p style={{ textAlign: 'left', fontSize: '16px' }}>
            Select an emissions factor
            <Tooltip title="Select an appropriate emissions factor">
              <Button
                icon={<QuestionCircleOutlined />}
                type="link"
                style={{ color: 'grey' }}
              ></Button>
            </Tooltip>
          </p>
          <Input.Search
            placeholder="Enter material or process (e.g., 'citric acid, steel production')"
            enterButton={
              <Button id="search-btn" type="primary" icon={<SearchOutlined />}>
                Search
              </Button>
            }
            id="search"
            size="large"
            value={emissionsFactorSearchValue}
            onChange={(e) => setEmissionsFactorSearchValue(e.target.value)}
            onSearch={handleEmissionsFactorSearch}
            style={{ marginBottom: '20px' }}
            loading={searchEmissionsFactorsIsLoading}
          />
          <DataTable
            bordered
            pageSize={5}
            id={'exchange-ef-table'}
            key={emissionsFactorsSearchResultsTableKey}
            data={emissionsFactorsSearchResults}
            columns={IntermediateExchangeEmissionsFactorTableColumns}
            searchable
            tableLoading={searchEmissionsFactorsIsLoading}
          />
        </>
      ),
    },
    {
      title: (
        <>
          <p style={{ width: 120, paddingTop: 8 }}>Exchange Information</p>
        </>
      ),
      content: (
        <>
          <p style={{ textAlign: 'left', fontSize: '16px' }}>
            Enter the exchange information
            <Tooltip title="Enter the exchange information">
              <Button
                icon={<QuestionCircleOutlined />}
                type="link"
                style={{ color: 'grey' }}
              ></Button>
            </Tooltip>
          </p>
          <Form
            form={addExchangeForm}
            layout="vertical"
            style={{ maxWidth: 600 }}
            id="add-intermediate-exchange-form"
          >
            <Form.Item style={{ display: 'none' }} name="id">
              <Input type="hidden" />
            </Form.Item>

            <Form.Item
              label="Input Name"
              name="exchangeName"
              rules={[{ required: true, message: 'Input name is required' }]}
              tooltip="Specify the name of the input"
            >
              <Input placeholder="eg: Electricity, Wastewater, Heat,..." />
            </Form.Item>

            <Row gutter={16}>
              <Col flex="1">
                <Form.Item
                  label="Amount"
                  name="amount"
                  rules={[{ required: true, message: 'amount is required' }]}
                  tooltip="Specify the amount with units"
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="0.122"
                  />
                </Form.Item>
              </Col>
              <Col flex="1">
                <Form.Item name="unit" label="Unit" required>
                  <Input style={{ width: '100%' }} readOnly />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </>
      ),
    },
    {
      title: (
        <>
          <p style={{ width: 120, paddingTop: 8 }}>Finish</p>
        </>
      ),
      content: (
        <>
          <Descriptions
            labelStyle={{ width: '30%' }}
            column={1}
            style={{ paddingRight: '40px', textAlign: 'left' }}
            bordered
            title="Exchange Info"
          >
            <Descriptions.Item
              label={
                <span style={{ fontWeight: 'bold', color: 'black' }}>
                  Input Name
                </span>
              }
            >
              {addExchangeForm.getFieldValue('exchangeName')}
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <span style={{ fontWeight: 'bold', color: 'black' }}>
                  Amount
                </span>
              }
            >
              {addExchangeForm.getFieldValue('amount')} (
              {addExchangeForm.getFieldValue('unit')})
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <span style={{ fontWeight: 'bold', color: 'black' }}>
                  Emissions Factor
                </span>
              }
            >
              {selectedExchangeActivity?.activityName} (
              {selectedExchangeActivity?.geography})
            </Descriptions.Item>
          </Descriptions>
        </>
      ),
    },
  ]

  return (
    <Drawer
      title="Dataset Details"
      placement="right"
      width="80%"
      onClose={onClose}
      open={isOpen}
      destroyOnClose
    >
      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: _parentActivity?.activityName,
            children: (
              <Tabs
                defaultActiveKey="1"
                tabPosition="left"
                style={{ height: 600 }}
                items={[
                  {
                    key: '1',
                    label: 'Overview',
                    children: (
                      <div className="activity-overview">
                        <Card
                          title={
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <span style={{ fontSize: '18px', fontWeight: 'bold' }}>
                                Dataset Summary
                                {editMode && !sharedScope && (
                                  <EditOutlined
                                    style={{ cursor: 'pointer', marginLeft: '10px', color: 'blue' }}
                                    onClick={() => setIsEditActivityDrawerOpen(true)}
                                  />
                                )}
                              </span>
                            </div>
                          }
                          bordered={false}
                          style={{ width: '100%', marginBottom: '20px' }}
                        >
                          <Descriptions
                            bordered
                            column={{ xxl: 1, xl: 1, lg: 1, md: 1, sm: 1, xs: 1 }}
                            labelStyle={{ fontWeight: 'bold', width: '200px' }}
                          >
                            <Descriptions.Item label="Activity Name">
                              {_parentActivity?.activityName || 'N/A'}
                            </Descriptions.Item>
                            <Descriptions.Item label="Reference Product">
                              {_parentActivity?.referenceProduct || 'N/A'}
                            </Descriptions.Item>
                            <Descriptions.Item label="Geography">
                              {_parentActivity?.geography || 'N/A'}
                            </Descriptions.Item>
                            <Descriptions.Item label="Source">
                              <Tag color="blue">{_parentActivity?.source || 'N/A'}</Tag>
                            </Descriptions.Item>
                            <Descriptions.Item label="Unit">
                              {_parentActivity?.unit || 'N/A'}
                            </Descriptions.Item>
                            {_parentActivity?.isTenant && (
                              <Descriptions.Item label="Custom Activity">
                                <Tag color="green">Yes</Tag>
                              </Descriptions.Item>
                            )}
                          </Descriptions>
                        </Card>

                        <Card
                          title={
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <span style={{ fontSize: '18px', fontWeight: 'bold' }}>
                                Dataset description
                                {editMode && !sharedScope && (
                                  <EditOutlined
                                    style={{ cursor: 'pointer', marginLeft: '10px', color: 'blue' }}
                                    onClick={() => setIsEditActivityDrawerOpen(true)}
                                  />
                                )}
                              </span>
                            </div>
                          }
                          bordered={false}
                          style={{ width: '100%' }}
                        >
                          <p>{_parentActivity?.description || 'N/A'}</p>
                        </Card>
                      </div>
                    ),
                  },
                  {
                    key: '2',
                    label: 'Intermediate Activities',
                    children: (
                      <>
                        {editMode && !sharedScope? (
                          <Button
                            style={{
                              marginRight: 10,
                              backgroundColor: '#f3c314d4',
                              float: 'left',
                            }}
                            onClick={() => setIsAddExchangeModalOpen(true)}
                          >
                            <Space>
                              <PlusOutlined />
                              Add Intermediate Exchange
                            </Space>
                          </Button>
                        ) : null}
                        <DataTable
                          searchable
                          bordered
                          scroll={{ x: '100%' }}
                          key={intermediateExchangesTableKey}
                          columns={columns}
                          data={intermediateExchanges}
                          tableLoading={loading || queryLoading}
                        />
                      </>
                    ),
                  },
                ]}
              />
            ),
          },
        ]}
      />
      <Modal
        title={editingExchange ? 'Edit Exchange' : 'Add Exchange'}
        open={isAddExchangeModalOpen}
        onCancel={handleCloseModal}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Steps
          labelPlacement="vertical"
          style={{
            marginTop: '20px',
            paddingLeft: '20px',
            paddingRight: '20px',
          }}
          size="small"
          direction="horizontal"
          current={currentAddExchangeStep}
          items={addExchangeSteps.map((step) => ({ title: step.title }))}
          onChange={(step) => handleAddExchangeStepChange(step)}
        />
        <Divider orientation="left"></Divider>
        {addExchangeSteps[currentAddExchangeStep].content}
        <div style={{ marginTop: 24, textAlign: 'right' }}>
          <Space>
            {currentAddExchangeStep > 0 && (
              <Button
                style={{
                  fontWeight: 600,
                  color: '#f3c314d4',
                  border: '1px solid #f3c314d4',
                }}
                onClick={() => setCurrentAddExchangeStep(0)}
              >
                Back
              </Button>
            )}
            {currentAddExchangeStep === addExchangeSteps.length - 1 ? (
              <Button
                style={{
                  backgroundColor: '#f3c314d4',
                  color: 'white',
                  fontWeight: 600,
                }}
                id='add-exchange-submit'
                loading={
                  createExchangeIsLoading ||
                  udpateExchangeIsLoading ||
                  deleteExchangeIsLoading
                }
                onClick={handleCreateOrUpdateExchange}
              >
                {editingExchange ? 'Update' : 'Finish & Submit'}
              </Button>
            ) : (
              <Button
                style={{
                  backgroundColor: '#f3c314d4',
                  color: 'white',
                  fontWeight: 600,
                }}
                onClick={() => addExchangeStepNext()}
              >
                Next
              </Button>
            )}
          </Space>
        </div>
      </Modal>
      <Drawer
        title="Edit Activity Details"
        open={isEditActivityDrawerOpen}
        onClose={() => setIsEditActivityDrawerOpen(false)}
        width={500}
        extra={
          <Space>
            <Button onClick={() => setIsEditActivityDrawerOpen(false)}>Cancel</Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              loading={updateEFIsLoading || loading}
              onClick={handleEditActivity}
            >
              Save
            </Button>
          </Space>
        }
      >
        <Spin spinning={loading}>
          <Form form={editActivityForm} layout="vertical">
            <Form.Item
              name="activityName"
              label="Activity Name"
              rules={[{ required: true, message: 'Please enter activity name' }]}
            >
              <Input placeholder="Enter activity name" />
            </Form.Item>

            <Form.Item
              name="referenceProduct"
              label="Reference Product"
              rules={[{ required: true, message: 'Please enter reference product' }]}
            >
              <Input placeholder="Enter reference product" />
            </Form.Item>

            <Form.Item
              name="description"
              label="Description"
            >
              <Input.TextArea rows={5} placeholder="Enter description" />
            </Form.Item>
          </Form>
        </Spin>
      </Drawer>
    </Drawer>
  )
}

export default IntermediateExchanges
