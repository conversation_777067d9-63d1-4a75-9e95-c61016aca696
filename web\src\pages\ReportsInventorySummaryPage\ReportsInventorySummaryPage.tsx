import { Link, routes } from '@redwoodjs/router'
import { MetaTags } from '@redwoodjs/web'
import { Layout } from 'antd'
import InventoryReportCell from 'src/components/InventoryReportCell'

const { Content } = Layout

const ReportsInventorySummaryPage = () => {
  return (
    <>
      <MetaTags title="Reports" description="Reports" />
      <Content>
        <Layout>
          <Content>
            <InventoryReportCell/>
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default ReportsInventorySummaryPage
