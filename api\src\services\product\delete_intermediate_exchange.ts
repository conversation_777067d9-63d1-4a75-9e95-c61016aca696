import { context } from '@redwoodjs/graphql-server'
import { OrgMemberInfo } from '@propelauth/node'
import axios from 'axios'
import { sanitizeInput } from 'src/lib/helper'
import { GraphQLError } from 'graphql'

export const deleteIntermediateExchange = async ({ exchangeId }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    await axios.delete(
      `${process.env.LCA_API_ENDPOINT}/emissions-factors/${tenantID}/exchange/${sanitizeInput(exchangeId)}`
    )

    return true

  } catch (error) {
    let errorMessage = 'Error deleting intermediate exchange. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
