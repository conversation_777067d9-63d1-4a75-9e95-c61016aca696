import React, { useMemo, useRef, useState } from 'react'
import { useReactToPrint } from 'react-to-print'
import type { ProductsQuery } from 'types/graphql'
import {
  type CellSuccessProps,
  type CellFailureProps,
  useMutation,
  useQuery,
} from '@redwoodjs/web'
import { Image, Row, Col, Tag, Flex, Card, Button, message } from 'antd'
import moment from 'moment'
import { saveAs } from 'file-saver'
import DataTable from '../DataTable/DataTable'

import {
  ArrowLeftOutlined,
  PrinterOutlined,
  DownloadOutlined,
} from '@ant-design/icons'
import { Link, navigate, routes } from '@redwoodjs/router'
import { getOrgMemberInfo, useAuth } from 'src/auth'
import ErrorHandler from '../ErrorHandler/ErrorHandler'
import InteractiveTagComponent from '../InteractiveTagComponent/InteractiveTagComponent'
import LoadingSkeleton from '../LoadingSkeleton/LoadingSkeleton'
import { renderImpactFactorUnit } from 'src/utils/helper'

export const QUERY = gql`
  query ProductsQuery {
    getProducts {
      productName
      productId
      brand
      category
      annualSalesVolumeUnits
      imageUrl
      totalEmissions
      clonedFromProductId
      materialEmissions
      packagingMaterialEmissions
      manufacturingEmissions
      distributionEmissions
      consumerUseEmissions
      eolEmissions
      tags
      attributes {
        productId
        key
        value
      }
    }
  }
`

const UPDATE_PRODUCT_TAGS = gql`
  mutation UpdateProductTags($productId: String!, $tags: [String!]!) {
    updateProductTags(productId: $productId, tags: $tags)
  }
`

export const Loading = () => <LoadingSkeleton />

export const Failure = ({ error }: CellFailureProps<ProductsQuery>) => (
  <ErrorHandler error={error} />
)

export const Success = (getProducts: CellSuccessProps<ProductsQuery>) => {
  if (!getProducts) {
    return <ErrorHandler />
  }

  const products = getProducts.getProducts
    .filter((product) => product.clonedFromProductId === null)
    .map((product) => ({
      key: product.productId,
      ...product,
    }))

  const { userMetadata } = useAuth()
  const orgMemberInfo = getOrgMemberInfo(userMetadata)

  const orgName =
    orgMemberInfo && orgMemberInfo.orgName ? orgMemberInfo.orgName : null

  const tenantID =
    orgMemberInfo && orgMemberInfo.urlSafeOrgName
      ? orgMemberInfo.urlSafeOrgName
      : null

  const { refetch: refetchProducts } = useQuery(QUERY)
  const componentRef = useRef()
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    pageStyle: `
      @page {
        size: landscape;
      }
    `,
  })

  const [updateProductTags, { loading: updateProductTagsIsLoading }] =
    useMutation(UPDATE_PRODUCT_TAGS)

  const [tableData, setTableData] = useState(products)
  const [tableKey, setTableKey] = useState(Date.now())

  const [filteredData, setFilteredData] = useState(products)

  const handleSave = async (updatedProduct) => {
    try {
      await updateProductTags({
        variables: {
          productId: updatedProduct.productId,
          tags: updatedProduct.tags,
        },
      })

      await refetchProducts()

      const newProducts = tableData.map((product) =>
        product.productId === updatedProduct.productId
          ? { ...product, ...updatedProduct }
          : product
      )

      setTableData(newProducts)
      setTableKey(Date.now())
    } catch (error) {
      console.error(
        `Failed to update tags for product ${updatedProduct.productName}`,
        error
      )
      message.error('Failed to update tags for product')
    }
  }

  const getAllUniqueAttributeKeys = (data) => {
    const keys = new Set()
    data.forEach((product) => {
      if (product.attributes) {
        product.attributes.forEach((attr) => keys.add(attr.key))
      }
    })
    return Array.from(keys)
  }

  const exportToCsv = (data) => {
    const attributeKeys = getAllUniqueAttributeKeys(data)

    const headers = [
      'Product ID',
      'Product',
      'Category',
      'Brand',
      'Annual Sales Volume(units)',
      `Raw Material Emissions (${renderImpactFactorUnit(userMetadata)})`,
      `Packaging Emissions (${renderImpactFactorUnit(userMetadata)})`,
      `Manufacturing Emissions (${renderImpactFactorUnit(userMetadata)})`,
      `Distribution Emissions (${renderImpactFactorUnit(userMetadata)})`,
      `Consumer Use Emissions (${renderImpactFactorUnit(userMetadata)})`,
      `End of Life Emissions (${renderImpactFactorUnit(userMetadata)})`,
      `Carbon Footprint (${renderImpactFactorUnit(userMetadata)})`,
      'Tags',
      ...attributeKeys,
    ]
    const csvData = [headers]

    data.forEach((product) => {
      const attributeValues = {}
      if (product.attributes) {
        product.attributes.forEach((attr) => {
          attributeValues[attr.key] = attr.value
        })
      }

      const row = [
        product.productId,
        product.productName,
        product.category,
        product.brand,
        product.annualSalesVolumeUnits,
        product.materialEmissions,
        product.packagingMaterialEmissions,
        product.manufacturingEmissions,
        product.distributionEmissions,
        product.consumerUseEmissions,
        product.eolEmissions,
        product.totalEmissions,
        `"${product.tags.join(',').replace(/"/g, '""')}"`,
        ...attributeKeys.map((key) => attributeValues[key] || ''),
      ]
      csvData.push(row)
    })

    const blob = new Blob([csvData.join('\n')], {
      type: 'text/csv;charset=utf-8',
    })
    saveAs(blob, 'CarbonBright_Product_Inventory_Summary_Report.csv')
  }

  const columns = useMemo(
    () => [
      {
        title: 'Product ID',
        dataIndex: 'productId',
        render: (text, record) => (
          <Link
            to={routes.productDetail({
              tenantID: tenantID,
              productId: encodeURIComponent(record.productId),
            })}
          >
            {text}
          </Link>
        ),
        sorter: true,
      },
      {
        title: 'Product',
        dataIndex: 'productName',
        render: (text, record) => (
          <Link
            to={routes.productDetail({
              tenantID: tenantID,
              productId: encodeURIComponent(record.productId),
            })}
          >
            {text}
          </Link>
        ),
        sorter: true,
      },
      {
        title: 'Category',
        dataIndex: 'category',
        sorter: true,
      },
      {
        title: 'Brand',
        dataIndex: 'brand',
        sorter: true,
      },
      {
        title: `Raw Material Emissions (${renderImpactFactorUnit(userMetadata)})`,
        dataIndex: 'materialEmissions',
        render: (text) => parseFloat(text).toFixed(4),
        sorter: true,
      },
      {
        title: `Packaging Emissions (${renderImpactFactorUnit(userMetadata)})`,
        dataIndex: 'packagingMaterialEmissions',
        render: (text) => parseFloat(text).toFixed(4),
        sorter: true,
      },
      {
        title: `Manufacturing Emissions (${renderImpactFactorUnit(userMetadata)})`,
        dataIndex: 'manufacturingEmissions',
        render: (text) => parseFloat(text).toFixed(4),
        sorter: true,
      },
      {
        title: `Distribution Emissions (${renderImpactFactorUnit(userMetadata)})`,
        dataIndex: 'distributionEmissions',
        render: (text) => parseFloat(text).toFixed(4),
        sorter: true,
      },
      {
        title: `Consumer Use Emissions (${renderImpactFactorUnit(userMetadata)})`,
        dataIndex: 'consumerUseEmissions',
        render: (text) => parseFloat(text).toFixed(4),
        sorter: true,
      },
      {
        title: `End of Life Emissions (${renderImpactFactorUnit(userMetadata)})`,
        dataIndex: 'eolEmissions',
        render: (text) => parseFloat(text).toFixed(4),
        sorter: true,
      },
      {
        title: `Carbon Footprint (${renderImpactFactorUnit(userMetadata)})`,
        dataIndex: 'totalEmissions',
        render: (text: string) => (
          <Tag color="blue">{parseFloat(text).toFixed(4)}</Tag>
        ),
        sorter: true,
      },
      {
        title: 'Tags',
        dataIndex: 'tags',
        key: 'tags',
        render: (tags, record) => (
          <InteractiveTagComponent
            record={record}
            handleSave={handleSave}
            data={tableData}
            loading={updateProductTagsIsLoading}
          />
        ),
        width: '15%',
        filters: (() => {
          const allTags = Array.from(
            new Set(tableData.flatMap((product) => product.tags || []))
          )
          return allTags.map((tag) => ({ text: tag, value: tag }))
        })(),
        filterSearch: true,
      },
    ],
    [tableData, handleSave]
  )

  return (
    <>
      <Row style={{ marginTop: '10px' }}>
        <Col flex="auto">
          <Button
            type="link"
            style={{ color: 'black' }}
            onClick={() => navigate(routes.reports())}
          >
            <b>
              <ArrowLeftOutlined /> &nbsp;Back
            </b>
          </Button>
        </Col>
        <Col flex="0">
          <Flex gap="small">
            <Button
              type="link"
              style={{ color: 'black' }}
              onClick={handlePrint}
            >
              <p style={{ fontSize: '18px' }}>
                <PrinterOutlined />
              </p>
            </Button>
            <Button
              type="link"
              style={{ color: 'black' }}
              onClick={() => exportToCsv(filteredData)}
            >
              <p style={{ fontSize: '18px' }}>
                <DownloadOutlined />
              </p>
            </Button>
          </Flex>
        </Col>
      </Row>
      <Card ref={componentRef} style={{ marginTop: '10px' }}>
        <Row>
          <Col flex="auto">
            <h1 style={{ fontSize: '18px', fontWeight: 'bold' }}>
              Product Inventory - Summary Report
            </h1>
          </Col>
          <Col flex="0">
            <Image
              width={150}
              preview={false}
              alt="CarbonBright"
              src="/images/logo.svg"
            />
          </Col>
        </Row>
        <Row>
          <Col flex="auto">
            <p style={{}}>Company: {orgName}</p>
          </Col>
          <Col flex="12">
            <p style={{}}>Creation Date: {moment().format('DD/MM/YYYY')}</p>
          </Col>
        </Row>
        <DataTable
          style={{ marginTop: '20px' }}
          key={tableKey}
          data={tableData}
          columns={columns}
          paginate={false}
          scroll={null}
          filters
          onDataFiltered={(newFilteredData) => setFilteredData(newFilteredData)}
        />
      </Card>
    </>
  )
}
