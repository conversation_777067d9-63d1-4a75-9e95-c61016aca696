import { context } from '@redwoodjs/graphql-server'
import axios from 'axios'
import { GraphQLError } from 'graphql'

export const getProductAttributes = async ({ productId }) => {
  try {
    const { orgName: tenantID } = context.currentUser.orgMemberInfo

    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/product-attributes/${tenantID}/${productId}`
    )

    if (!Array.isArray(response.data)) {
      throw new Error('Unexpected response format from the server.')
    }

    return response.data.map((attribute) => ({
      id: attribute.id,
      productId: attribute.product_id,
      key: attribute.key,
      value: attribute.value,
    }))
  } catch (error) {
    let errorMessage = 'Error fetching product attributes. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
