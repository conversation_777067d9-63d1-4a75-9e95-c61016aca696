import axios from 'axios'
import { GraphQLError } from 'graphql'

export const getProductCategories = async () => {
  try {
    const response = await axios.get(
      `${process.env.LCA_API_ENDPOINT}/product-categories`
    )

    const productCategories = response.data

    if (!Array.isArray(productCategories)) {
      throw new Error('Unexpected response format from the server.')
    }

    return productCategories
  } catch (error) {
    let errorMessage = 'Error fetching product categories. Please try again'

    if (error.response) {
      errorMessage = error.response.data.detail || errorMessage
    }

    throw new GraphQLError(errorMessage)
  }
}
