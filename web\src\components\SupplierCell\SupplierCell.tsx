import type { SuppliersQuery } from 'types/graphql'
import { type CellSuccessProps, type CellFailureProps } from '@redwoodjs/web'
import { Checkbox, Tag } from 'antd'
import DataTable from '../DataTable/DataTable'
import { Link, routes } from '@redwoodjs/router'
import ErrorHandler from '../ErrorHandler/ErrorHandler'
import LoadingSkeleton from '../LoadingSkeleton/LoadingSkeleton'
import { useAuth } from 'src/auth'
import { renderImpactFactorUnit } from 'src/utils/helper'

export const QUERY = gql`
  query SuppliersQuery {
    getSuppliers {
      supplierName
      supplierId
      supplierLevel
      supplierType
      country
      city
      address
      totalProducts
      products {
        productName
        productId
        category
        brand
        totalEmissions
        scope3Emissions
        annualSalesVolumeUnits
      }
      scope3Emissions
    }
  }
`

export const Loading = () => <LoadingSkeleton />

export const Failure = ({ error }: CellFailureProps) => (
  <ErrorHandler error={error} />
)

export const Success = ({ getSuppliers }: CellSuccessProps<SuppliersQuery>) => {
  if (!getSuppliers) {
    return <ErrorHandler />
  }

  const { userMetadata } = useAuth()

  const columns = [
    {
      title: 'Select',
      dataIndex: 'supplierId',
      render: (key, record) => <Checkbox value={key} />,
    },
    {
      title: 'Supplier Name',
      dataIndex: 'supplierName',
      width: '20%',
      render: (text, record) => (
        <Link
          to={routes.supplierInfo({
            supplierId: record.supplierId.toString(),
          })}
        >
          <b>{text}</b>
        </Link>
      ),
      sorter: true,
    },
    {
      title: 'Country',
      dataIndex: 'country',
      sorter: true,
    },
    {
      title: 'Level',
      dataIndex: 'supplierLevel',
      render: (supplierLevel: string) => <p>Tier {supplierLevel}</p>,
    },
    {
      title: 'Type',
      dataIndex: 'supplierType',
      sorter: true,
    },
    {
      title: 'Products',
      dataIndex: 'totalProducts',
      sorter: true,
    },
    {
      title: `Scope 3 Emissions (${renderImpactFactorUnit(userMetadata)})`,
      dataIndex: 'scope3Emissions',
      render: (text: string) => (
        <Tag color="blue">
          {parseFloat(text).toLocaleString(undefined, {
            minimumFractionDigits: 4,
          })}
        </Tag>
      ),
      sorter: true,
    },
    {
      title: 'Status',
      dataIndex: '',
      render: (text: string) => (
        <p style={{ color: 'green' }}>&#x2022; Active</p>
      ),
    },
  ]

  return (
    <>
      <DataTable
        key={getSuppliers.length}
        data={getSuppliers}
        columns={columns}
        searchable
      />
    </>
  )
}
