import { Link, navigate, routes } from '@redwoodjs/router'
import { MetaTags } from '@redwoodjs/web'
import { Layout, Menu, Modal, theme } from 'antd'
import { useAuth } from 'src/auth'
const { Content } = Layout
import SupplierCell from 'src/components/SupplierCell'
import UpgradePlan from 'src/components/UpgradePlan/UpgradePlan'

const SupplierPage = () => {
  const { userMetadata } = useAuth()

  return (
    <>
      <MetaTags title="Suppliers" description="Supplier page" />

      <Content>
        <Layout>
          <Content style={{ minHeight: 280 }}>
            {userMetadata.user.metadata?.isTrialUser ? (
              <UpgradePlan
                visible={true}
                onClose={() => navigate(routes.home())}
              />
            ) : (
              <SupplierCell />
            )}
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default SupplierPage
