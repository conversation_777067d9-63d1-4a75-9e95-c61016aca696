import { navigate, routes } from '@redwoodjs/router'
import { Metadata } from '@redwoodjs/web'
import { Card, Col, Row, Layout, Badge, Tag } from 'antd'
import Meta from 'antd/es/card/Meta'
import { useState } from 'react'
import { useAuth } from 'src/auth'
import UpgradePlan from 'src/components/UpgradePlan/UpgradePlan'

const { Content } = Layout

const ReportsPage = () => {
  const { userMetadata } = useAuth()
  const [isUpgradePlanModalVisible, setIsUpgradePlanModalVisible] = useState(false);

  return (
    <>
      <Metadata title="Reports" description="Reports" />
      <Content>
        <Layout>
          <UpgradePlan
            visible={isUpgradePlanModalVisible}
            onClose={() => setIsUpgradePlanModalVisible(false)}
          />
          <Content>
            <Card
              title="Summary Reports"
              bordered={false}
              style={{ marginTop: '15px' }}
            >
              <Card
                hoverable
                style={{ width: 220 }}
                cover={
                  <img
                    alt="Product Inventory Summary"
                    src="/images/reports_card_placeholder_inventory_summary.png"
                  />
                }
                onClick={(e) => {
                  userMetadata.user.metadata?.isTrialUser
                    ? setIsUpgradePlanModalVisible(true)
                    : navigate(routes.reportsInventorySummary())
                }}
              >
                {userMetadata.user.metadata?.isTrialUser && (
                  <Tag
                    style={{
                      position: 'absolute',
                      zIndex: 1,
                      right: -25,
                      top: -10,
                    }}
                    color="#FFC000"
                  >
                    PRO
                  </Tag>
                )}
                <p
                  style={{
                    marginTop: '-10px',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    lineHeight: '1.25',
                  }}
                >
                  Product Inventory Summary
                </p>
                <Meta
                  style={{ marginTop: '10px', fontSize: '10px' }}
                  description="Provides summary of Product Inventory and associated impact metrics"
                />
              </Card>
            </Card>

            <Card
              title="Other Reports"
              bordered={false}
              style={{ marginTop: '15px' }}
            >
              <Row>
                <Col span={4}>
                  <Card
                    hoverable
                    style={{ width: 220 }}
                    cover={
                      <img
                        alt="Product Emissions by Category"
                        src="/images/reports_card_placeholder_emissions_by_category.png"
                      />
                    }
                    onClick={(e) => {
                      userMetadata.user.metadata?.isTrialUser
                        ? setIsUpgradePlanModalVisible(true)
                        : navigate(routes.reportsCategoryEmissions())
                    }}
                  >
                    {userMetadata.user.metadata?.isTrialUser && (
                      <Tag
                        style={{
                          position: 'absolute',
                          zIndex: 1,
                          right: -25,
                          top: -10,
                        }}
                        color="#FFC000"
                      >
                        PRO
                      </Tag>
                    )}
                    <p
                      style={{
                        marginTop: '-10px',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        lineHeight: '1.25',
                      }}
                    >
                      Product Emissions by Category
                    </p>
                    <Meta
                      style={{ marginTop: '10px', fontSize: '10px' }}
                      description="Provides an overview of Emissions by Product Category"
                    />
                  </Card>
                </Col>
                <Col span={4}>
                  <Card
                    hoverable
                    style={{ width: 220 }}
                    cover={
                      <img
                        alt="Supplier Emissions Summary"
                        src="/images/reports_card_placeholder_emissions_by_category.png"
                      />
                    }
                    onClick={(e) => {
                      userMetadata.user.metadata?.isTrialUser
                        ? setIsUpgradePlanModalVisible(true)
                        : navigate(routes.reportsSupplierEmissions())
                    }}
                  >
                    {userMetadata.user.metadata?.isTrialUser && (
                      <Tag
                        style={{
                          float: 'right',
                          position: 'absolute',
                          zIndex: 1,
                          right: -25,
                          top: -10,
                        }}
                        color="#FFC000"
                      >
                        PRO
                      </Tag>
                    )}

                    <p
                      style={{
                        marginTop: '-10px',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        lineHeight: '1.25',
                      }}
                    >
                      Supplier Emissions Summary
                    </p>
                    <Meta
                      style={{ marginTop: '10px', fontSize: '10px' }}
                      description="Provides an overview of Emissions by Product Supplier"
                    />
                  </Card>
                </Col>
              </Row>
            </Card>
          </Content>
        </Layout>
      </Content>
    </>
  )
}

export default ReportsPage
